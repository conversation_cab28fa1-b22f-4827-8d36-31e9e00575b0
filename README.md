# 🏷️ 智能标签管理系统

> 基于WebSocket的实时通信系统，支持多客户端连接和消息转发

[![Node.js](https://img.shields.io/badge/Node.js-14%2B-green.svg)](https://nodejs.org/)
[![WebSocket](https://img.shields.io/badge/WebSocket-RFC6455-blue.svg)](https://tools.ietf.org/html/rfc6455)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📋 项目概述

智能标签管理系统是一个基于WebSocket技术的实时通信平台，采用前后端分离架构设计。系统支持多客户端同时连接，实现消息的实时转发和广播功能。

### ✨ 核心特性

- 🚀 **高性能WebSocket服务器** - 基于Node.js和ws库构建
- 🔄 **实时消息转发** - 支持多客户端间的实时通信
- 🎨 **现代化前端界面** - 原生HTML/CSS/JS，响应式设计
- ⚙️ **灵活配置管理** - 支持环境变量和配置文件
- 📊 **性能监控** - 实时统计和健康检查
- 🛡️ **安全防护** - 速率限制和输入验证
- 📝 **详细日志** - 结构化日志记录
- 🔧 **优雅关闭** - 安全的服务停止机制

### 🏗️ 技术架构

```
┌─────────────────┐    WebSocket     ┌─────────────────┐
│   前端客户端     │ ◄──────────────► │   后端服务器     │
│                │                  │                │
│ • HTML/CSS/JS  │                  │ • Node.js      │
│ • 原生WebSocket │                  │ • ws库         │
│ • 响应式设计    │                  │ • 性能监控      │
│ • 配置管理      │                  │ • 日志系统      │
└─────────────────┘                  └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- **Node.js**: 14.0.0 或更高版本
- **npm**: 6.0.0 或更高版本
- **浏览器**: 支持WebSocket的现代浏览器

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd SmartTip
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动服务器**
   ```bash
   # 开发模式
   npm run dev
   
   # 生产模式
   npm start
   ```

4. **打开前端界面**
   - 在浏览器中打开 `frontend/index.html`
   - 或使用本地服务器：`python -m http.server 3000`（在frontend目录下）

### 🎯 使用方法

1. **连接服务器**
   - 在前端界面输入服务器地址（默认：localhost）
   - 输入端口号（默认：8080）
   - 点击"连接服务器"按钮

2. **发送消息**
   - 连接成功后，在消息输入框中输入内容
   - 点击"发送消息"或按Enter键发送
   - 消息会自动转发给所有其他连接的客户端

3. **查看状态**
   - 连接状态：实时显示连接状态
   - 系统信息：显示发送/接收消息数、连接时长等
   - 消息历史：显示所有收发的消息记录

## ⚙️ 配置说明

### 后端配置

服务器配置文件位于 `backend/config.js`，支持以下配置项：

#### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `WS_PORT` | 8080 | WebSocket服务器端口 |
| `WS_HOST` | localhost | WebSocket服务器地址 |
| `MAX_CONNECTIONS` | 100 | 最大连接数 |
| `HEARTBEAT_INTERVAL` | 30000 | 心跳检测间隔(毫秒) |
| `LOG_LEVEL` | info | 日志级别(debug/info/warn/error) |
| `ENABLE_RATE_LIMIT` | true | 是否启用速率限制 |
| `MAX_MESSAGES_PER_MINUTE` | 60 | 每分钟最大消息数 |

#### 配置示例

```bash
# 设置环境变量
export WS_PORT=9090
export WS_HOST=0.0.0.0
export MAX_CONNECTIONS=200
export LOG_LEVEL=debug

# 启动服务器
npm start
```

### 前端配置

前端配置文件位于 `frontend/config.js`，主要配置项：

- **连接配置**: 默认服务器地址、重连设置
- **界面配置**: 消息显示限制、自动滚动等
- **存储配置**: 本地存储键名和配额限制

## 📊 监控和日志

### 性能监控

系统提供实时性能监控，包括：

- **连接统计**: 当前连接数、总连接数
- **消息统计**: 消息发送/接收数量、处理速率
- **系统资源**: 内存使用、CPU占用
- **错误统计**: 错误数量和类型

### 日志系统

支持多级别日志记录：

- **控制台输出**: 彩色日志，便于开发调试
- **文件日志**: 可选的文件日志记录（需配置）
- **结构化数据**: JSON格式，便于日志分析

日志级别：
- `debug`: 详细调试信息
- `info`: 一般信息记录
- `warn`: 警告信息
- `error`: 错误信息

## 🛡️ 安全特性

### 速率限制

- **IP级别限制**: 基于客户端IP的消息频率限制
- **自动封禁**: 超过限制自动临时封禁
- **动态解封**: 封禁期满自动解除

### 输入验证

- **消息大小限制**: 防止过大消息攻击
- **内容清理**: 移除潜在恶意字符
- **连接数限制**: 防止连接耗尽攻击

## 🔧 开发指南

### 项目结构

```
SmartTip/
├── backend/                 # 后端代码
│   ├── config.js           # 配置管理
│   └── server.js           # WebSocket服务器
├── frontend/               # 前端代码
│   ├── index.html          # 主页面
│   ├── style.css           # 样式文件
│   ├── script.js           # 客户端逻辑
│   └── config.js           # 前端配置
├── package.json            # 项目配置
└── README.md              # 项目文档
```

### 开发模式

```bash
# 启用调试模式
export NODE_ENV=development
export ENABLE_DEBUG=true
export LOG_LEVEL=debug

npm run dev
```

### 代码规范

- **注释**: 代码右侧使用 `#` 注释
- **配置**: 统一配置文件管理，避免重复定义
- **错误处理**: 完善的异常捕获和用户友好提示
- **性能**: 注重代码效率和资源使用优化

## 🐛 故障排除

### 常见问题

**Q: 服务器启动失败**
```
A: 检查端口是否被占用，确认Node.js版本是否符合要求
   lsof -i :8080  # 检查端口占用
   node --version # 检查Node.js版本
```

**Q: 前端无法连接服务器**
```
A: 1. 确认服务器已启动
   2. 检查防火墙设置
   3. 验证服务器地址和端口配置
```

**Q: 消息发送失败**
```
A: 1. 检查网络连接
   2. 确认消息大小未超过限制
   3. 查看是否触发速率限制
```

**Q: 性能问题**
```
A: 1. 检查系统资源使用情况
   2. 调整最大连接数配置
   3. 启用性能监控查看详细统计
```

### 日志分析

查看详细日志信息：

```bash
# 查看实时日志
tail -f logs/server-$(date +%Y-%m-%d).log

# 搜索错误日志
grep "ERROR" logs/server-*.log

# 分析连接统计
grep "性能统计" logs/server-*.log | tail -10
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 Wiki: [项目Wiki](https://github.com/your-repo/wiki)

---

**🎉 感谢使用智能标签管理系统！**

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品功能介绍</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 1rem;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .features {
            padding: 3rem 0;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-title {
            color: #3498db;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        .feature-title i {
            margin-right: 10px;
            font-size: 1.8rem;
        }
        .feature-content {
            color: #666;
        }
        footer {
            background-color: #2c3e50;
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 2rem;
        }
        @media (min-width: 768px) {
            .features-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <img src="C:\Users\<USER>\Desktop\Web\SmartTip\frontend\picture\logo.png" alt="产品Logo" class="logo">
            <h1>智能系统产品功能介绍</h1>
            <p>创新科技，智能体验</p>
        </div>
    </header>

    <section class="features">
        <div class="container">
            <div class="features-grid">
                <div class="feature-card">
                    <h2 class="feature-title">
                        <i class="fas fa-database"></i>
                        功能一：文件系统与Flash系统
                    </h2>
                    <div class="feature-content">
                        <p>基于GD25Q40芯片实现的高效文件系统及flash系统，提供稳定可靠的数据存储解决方案。支持快速读写、掉电保护，满足各类数据存储需求。</p>
                    </div>
                </div>

                <div class="feature-card">
                    <h2 class="feature-title">
                        <i class="fas fa-wifi"></i>
                        功能二：Zigbee智能屏幕更新
                    </h2>
                    <div class="feature-content">
                        <p>基于zigbee组网技术，实现远程智能标签屏幕的实时更新。低功耗、高可靠性的网络结构，确保标签信息及时同步更新，适用于零售、仓储等多种场景。</p>
                    </div>
                </div>

                <div class="feature-card">
                    <h2 class="feature-title">
                        <i class="fas fa-robot"></i>
                        功能三：智能AI系统
                    </h2>
                    <div class="feature-content">
                        <p>集成智谱AI技术的智能对话与价格更新系统。支持自然语言交互，智能分析市场动态，自动调整产品价格，提升运营效率与客户体验。</p>
                    </div>
                </div>

                <div class="feature-card">
                    <h2 class="feature-title">
                        <i class="fas fa-map-marker-alt"></i>
                        功能四：中枢定位系统
                    </h2>
                    <div class="feature-content">
                        <p>中枢系统内置高精度定位功能，实时追踪设备位置信息。支持室内外场景，精准定位，为智能管理提供空间信息支持。</p>
                    </div>
                </div>

                <div class="feature-card">
                    <h2 class="feature-title">
                        <i class="fas fa-desktop"></i>
                        功能五：串口屏显示系统
                    </h2>
                    <div class="feature-content">
                        <p>通过串口屏实时显示系统运行状态、参数及关键信息。直观的图形界面，便于用户监控系统健康状况，及时发现并处理潜在问题。</p>
                    </div>
                </div>
                
                <div class="feature-card">
                    <h2 class="feature-title">
                        <i class="fas fa-cloud"></i>
                        功能六：4G上云系统
                    </h2>
                    <div class="feature-content">
                        <p>使用DTU 4G模块实现设备无缝连接云端，通过自制WebSocket服务提供实时数据传输与远程监控能力。支持多终端接入，确保数据安全可靠传输，打造完整物联网解决方案。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>© 2023 智能系统产品. 保留所有权利。</p>
        </div>
    </footer>
</body>
</html>

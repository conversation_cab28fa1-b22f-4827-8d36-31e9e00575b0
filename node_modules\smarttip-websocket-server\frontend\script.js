// 智能标签管理系统 - WebSocket客户端逻辑

class SmartTipClient {
    constructor() {
        this.ws = null; // WebSocket连接实例
        this.isConnected = false; // 连接状态
        this.reconnectAttempts = 0; // 重连尝试次数
        this.reconnectTimer = null; // 重连定时器
        this.connectionStartTime = null; // 连接开始时间
        this.connectionTimer = null; // 连接时长计时器
        this.sentCount = 0; // 发送消息计数
        this.receivedCount = 0; // 接收消息计数
        this.clientId = null; // 客户端ID
        this.messageHistory = []; // 消息历史
        
        this.initializeElements(); // 初始化DOM元素
        this.bindEvents(); // 绑定事件
        this.loadStoredConfig(); // 加载存储的配置
        this.updateUI(); // 更新界面状态
    }
    
    // 初始化DOM元素引用
    initializeElements() {
        this.elements = {
            serverAddress: document.getElementById('serverAddress'),
            connectBtn: document.getElementById('connectBtn'),
            disconnectBtn: document.getElementById('disconnectBtn'),
            connectionStatus: document.getElementById('connectionStatus'),
            serverAddressDisplay: document.getElementById('serverAddressDisplay'),
            clientIdDisplay: document.getElementById('clientId'),
            messageContainer: document.getElementById('messageContainer'),
            messageInput: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            clearBtn: document.getElementById('clearBtn'),
            sentCount: document.getElementById('sentCount'),
            receivedCount: document.getElementById('receivedCount'),
            connectionTime: document.getElementById('connectionTime'),
            lastActivity: document.getElementById('lastActivity')
        };
    }
    
    // 绑定事件监听器
    bindEvents() {
        // 连接按钮事件
        this.elements.connectBtn.addEventListener('click', () => this.connect());
        this.elements.disconnectBtn.addEventListener('click', () => this.disconnect());
        
        // 消息发送事件
        this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        this.elements.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 清空消息事件
        this.elements.clearBtn.addEventListener('click', () => this.clearMessages());

        // 输入框变化事件
        this.elements.serverAddress.addEventListener('input', () => this.saveConfig());
        
        // 输入框变化事件
        this.elements.serverAddress.addEventListener('input', () => this.saveConfig());
        
        // 页面关闭事件
        window.addEventListener('beforeunload', () => {
            if (this.isConnected) {
                this.disconnect();
            }
        });
    }
    
    // 加载存储的配置
    loadStoredConfig() {
        try {
            const stored = localStorage.getItem('smarttip_server_config');
            if (stored) {
                const config = JSON.parse(stored);
                this.elements.serverAddress.value = config.address || this.getDefaultAddress();
            } else {
                // 如果没有存储的配置，使用当前页面的地址
                this.elements.serverAddress.value = this.getDefaultAddress();
            }
        } catch (error) {
            console.warn('加载配置失败:', error);
            // 出错时使用当前页面地址作为默认值
            this.elements.serverAddress.value = this.getDefaultAddress();
        }
    }

    // 获取默认服务器地址
    getDefaultAddress() {
        const host = window.location.hostname || 'localhost';
        const port = window.location.port || '8080';
        return `${host}:${port}`;
    }

    // 保存配置到本地存储
    saveConfig() {
        try {
            const config = {
                address: this.elements.serverAddress.value
            };
            localStorage.setItem('smarttip_server_config', JSON.stringify(config));
        } catch (error) {
            console.warn('保存配置失败:', error);
        }
    }
    
    // 连接WebSocket服务器
    connect() {
        if (this.isConnected) return;

        const address = this.elements.serverAddress.value.trim();
        if (!address) {
            this.handleConnectionError('请输入服务器地址');
            return;
        }

        // 构建WebSocket URL
        let url;
        if (address.startsWith('ws://') || address.startsWith('wss://')) {
            // 如果是完整的WebSocket URL，直接使用
            url = address;
        } else {
            // 如果是主机名:端口格式，添加ws://前缀
            url = `ws://${address}`;
        }

        // 保存实际连接的地址用于显示
        this.connectedAddress = address;

        this.updateConnectionStatus('connecting', '连接中...');
        this.addSystemMessage(`正在连接到 ${url}...`);

        try {
            this.ws = new WebSocket(url);
            this.setupWebSocketEvents();
        } catch (error) {
            this.handleConnectionError('连接失败: ' + error.message);
        }
    }
    
    // 设置WebSocket事件监听
    setupWebSocketEvents() {
        this.ws.onopen = (event) => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.connectionStartTime = new Date();
            this.startConnectionTimer();

            this.updateConnectionStatus('connected', '已连接');
            this.elements.serverAddressDisplay.textContent = this.connectedAddress;
            this.addSystemMessage(`✅ 成功连接到服务器: ${this.connectedAddress}`);
            this.updateUI();
        };
        
        this.ws.onmessage = (event) => {
            this.handleMessage(event.data);
            this.updateLastActivity();
        };
        
        this.ws.onclose = (event) => {
            this.isConnected = false;
            this.stopConnectionTimer();
            
            if (event.wasClean) {
                this.updateConnectionStatus('disconnected', '已断开');
                this.addSystemMessage(`🔌 连接已断开 (代码: ${event.code})`);
            } else {
                this.updateConnectionStatus('disconnected', '连接中断');
                this.addSystemMessage(`❌ 连接意外中断 (代码: ${event.code})`);
                this.attemptReconnect();
            }
            this.updateUI();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.addSystemMessage('❌ 连接发生错误');
        };
    }
    
    // 断开连接
    disconnect() {
        if (!this.isConnected || !this.ws) return;
        
        this.ws.close(1000, '用户主动断开');
        this.clearReconnectTimer();
    }
    
    // 处理接收到的消息
    handleMessage(data) {
        this.receivedCount++;
        this.elements.receivedCount.textContent = this.receivedCount;
        
        try {
            // 尝试解析JSON消息
            const message = JSON.parse(data);
            
            if (message.type === 'welcome') {
                this.clientId = message.clientId;
                this.elements.clientIdDisplay.textContent = this.clientId;
                this.addSystemMessage(`🎉 ${message.message} (ID: ${this.clientId})`);
            } else if (message.type === 'error') {
                this.addSystemMessage(`⚠️ 服务器错误: ${message.message}`);
            } else if (message.type === 'server_shutdown') {
                this.addSystemMessage(`🔄 ${message.message}`);
            } else {
                this.addReceivedMessage(JSON.stringify(message, null, 2));
            }
        } catch (error) {
            // 普通文本消息
            this.addReceivedMessage(data);
        }
    }
    
    // 发送消息
    sendMessage() {
        if (!this.isConnected || !this.ws) {
            this.addSystemMessage('❌ 请先连接到服务器');
            return;
        }
        
        const message = this.elements.messageInput.value.trim();
        if (!message) return;
        
        try {
            this.ws.send(message);
            this.sentCount++;
            this.elements.sentCount.textContent = this.sentCount;
            this.addSentMessage(message);
            this.elements.messageInput.value = '';
            this.updateLastActivity();
        } catch (error) {
            this.addSystemMessage('❌ 发送消息失败: ' + error.message);
        }
    }
    
    // 添加发送的消息到显示区域
    addSentMessage(message) {
        this.addMessage(message, 'sent', '我');
    }
    
    // 添加接收的消息到显示区域
    addReceivedMessage(message) {
        this.addMessage(message, 'received', '其他用户');
    }
    
    // 添加系统消息
    addSystemMessage(message) {
        this.addMessage(message, 'system', '系统');
    }
    
    // 添加消息到显示区域
    addMessage(content, type, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-item message-${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        messageDiv.innerHTML = `
            <div><strong>${sender}:</strong> ${this.escapeHtml(content)}</div>
            <div class="message-timestamp">${timestamp}</div>
        `;
        
        // 移除欢迎消息
        const welcomeMsg = this.elements.messageContainer.querySelector('.welcome-message');
        if (welcomeMsg) {
            welcomeMsg.remove();
        }
        
        this.elements.messageContainer.appendChild(messageDiv);
        
        // 限制消息数量
        const messages = this.elements.messageContainer.querySelectorAll('.message-item');
        if (messages.length > CONFIG.ui.messageDisplayLimit) {
            messages[0].remove();
        }
        
        // 自动滚动到底部
        if (CONFIG.ui.autoScroll) {
            this.elements.messageContainer.scrollTop = this.elements.messageContainer.scrollHeight;
        }
    }
    
    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // 清空消息
    clearMessages() {
        this.elements.messageContainer.innerHTML = `
            <div class="welcome-message">
                <p>👋 消息已清空！</p>
                <p>继续发送消息与其他用户交流。</p>
            </div>
        `;
    }
    
    // 更新连接状态
    updateConnectionStatus(status, text) {
        this.elements.connectionStatus.textContent = text;
        this.elements.connectionStatus.className = `status-value status-${status}`;
    }
    
    // 更新界面状态
    updateUI() {
        this.elements.connectBtn.disabled = this.isConnected;
        this.elements.disconnectBtn.disabled = !this.isConnected;
        this.elements.messageInput.disabled = !this.isConnected;
        this.elements.sendBtn.disabled = !this.isConnected;
        
        if (!this.isConnected) {
            this.elements.serverAddressDisplay.textContent = '-';
            this.elements.clientIdDisplay.textContent = '-';
        }
    }
    
    // 开始连接时长计时
    startConnectionTimer() {
        this.connectionTimer = setInterval(() => {
            if (this.connectionStartTime) {
                const duration = new Date() - this.connectionStartTime;
                const hours = Math.floor(duration / 3600000);
                const minutes = Math.floor((duration % 3600000) / 60000);
                const seconds = Math.floor((duration % 60000) / 1000);
                this.elements.connectionTime.textContent = 
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }, 1000);
    }
    
    // 停止连接时长计时
    stopConnectionTimer() {
        if (this.connectionTimer) {
            clearInterval(this.connectionTimer);
            this.connectionTimer = null;
        }
        this.elements.connectionTime.textContent = '00:00:00';
    }
    
    // 更新最后活动时间
    updateLastActivity() {
        this.elements.lastActivity.textContent = new Date().toLocaleTimeString();
    }
    
    // 尝试重连
    attemptReconnect() {
        if (this.reconnectAttempts >= CONFIG.websocket.maxReconnectAttempts) {
            this.addSystemMessage(`❌ 重连失败，已达到最大重连次数 (${CONFIG.websocket.maxReconnectAttempts})`);
            return;
        }
        
        this.reconnectAttempts++;
        this.addSystemMessage(`🔄 尝试重连 (${this.reconnectAttempts}/${CONFIG.websocket.maxReconnectAttempts})...`);
        
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, CONFIG.websocket.reconnectInterval);
    }
    
    // 清除重连定时器
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }
    
    // 处理连接错误
    handleConnectionError(message) {
        this.updateConnectionStatus('disconnected', '连接失败');
        this.addSystemMessage(`❌ ${message}`);
        this.updateUI();
    }
}

// 页面加载完成后初始化客户端
document.addEventListener('DOMContentLoaded', () => {
    window.smartTipClient = new SmartTipClient();
    console.log('🚀 智能标签管理系统客户端已初始化');
});

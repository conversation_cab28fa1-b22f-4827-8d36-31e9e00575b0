// WebSocket服务器主文件
const { WebSocketServer } = require('ws');
const fs = require('fs');
const path = require('path');
const config = require('./config');

// 创建日志目录
if (config.logging.enableFile && !fs.existsSync(config.logging.logDir)) {
    try {
        fs.mkdirSync(config.logging.logDir, { recursive: true });
        console.log(`📁 日志目录已创建: ${config.logging.logDir}`);
    } catch (error) {
        console.error('❌ 创建日志目录失败:', error.message);
    }
}

// 增强的日志系统
class Logger {
    constructor() {
        this.logLevels = { debug: 0, info: 1, warn: 2, error: 3 };
        this.currentLevel = this.logLevels[config.logging.level] || 1;
        this.logFile = config.logging.enableFile ?
            path.join(config.logging.logDir, `server-${new Date().toISOString().split('T')[0]}.log`) : null;
    }

    log(level, message, data = null, error = null) {
        if (this.logLevels[level] < this.currentLevel) return;

        const timestamp = new Date().toISOString();
        const logData = {
            timestamp,
            level: level.toUpperCase(),
            message,
            data,
            error: error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : null,
            pid: process.pid,
            memory: process.memoryUsage()
        };

        const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}${data ? ' ' + JSON.stringify(data) : ''}${error ? ' ERROR: ' + error.message : ''}`;

        // 控制台输出
        if (config.logging.enableConsole) {
            const colorMap = {
                debug: '\x1b[36m', // 青色
                info: '\x1b[32m',  // 绿色
                warn: '\x1b[33m',  // 黄色
                error: '\x1b[31m'  // 红色
            };
            console.log(`${colorMap[level] || ''}${logMessage}\x1b[0m`);
        }

        // 文件输出
        if (this.logFile) {
            try {
                fs.appendFileSync(this.logFile, logMessage + '\n');
            } catch (err) {
                console.error('写入日志文件失败:', err.message);
            }
        }

        return logData;
    }

    debug(message, data) { return this.log('debug', message, data); }
    info(message, data) { return this.log('info', message, data); }
    warn(message, data) { return this.log('warn', message, data); }
    error(message, data, error) { return this.log('error', message, data, error); }
}

const logger = new Logger();

// 系统信息记录
logger.info('智能标签管理系统WebSocket服务器启动', {
    version: config.app.version,
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    configSummary: config.getConfigSummary()
});

// 性能监控类
class PerformanceMonitor {
    constructor() {
        this.stats = {
            startTime: Date.now(),
            totalConnections: 0,
            currentConnections: 0,
            totalMessages: 0,
            totalErrors: 0,
            lastStatsTime: Date.now()
        };

        if (config.monitoring.enableStats) {
            this.startStatsReporting();
        }
    }

    incrementConnections() {
        this.stats.totalConnections++;
        this.stats.currentConnections++;
    }

    decrementConnections() {
        this.stats.currentConnections = Math.max(0, this.stats.currentConnections - 1);
    }

    incrementMessages() {
        this.stats.totalMessages++;
    }

    incrementErrors() {
        this.stats.totalErrors++;
    }

    getStats() {
        const now = Date.now();
        const uptime = now - this.stats.startTime;
        const memory = process.memoryUsage();

        return {
            ...this.stats,
            uptime,
            uptimeFormatted: this.formatUptime(uptime),
            memory: {
                rss: Math.round(memory.rss / 1024 / 1024) + 'MB',
                heapTotal: Math.round(memory.heapTotal / 1024 / 1024) + 'MB',
                heapUsed: Math.round(memory.heapUsed / 1024 / 1024) + 'MB',
                external: Math.round(memory.external / 1024 / 1024) + 'MB'
            },
            messagesPerSecond: this.stats.totalMessages / (uptime / 1000),
            connectionsPerHour: this.stats.totalConnections / (uptime / 3600000)
        };
    }

    formatUptime(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days}天 ${hours % 24}小时 ${minutes % 60}分钟`;
        if (hours > 0) return `${hours}小时 ${minutes % 60}分钟 ${seconds % 60}秒`;
        if (minutes > 0) return `${minutes}分钟 ${seconds % 60}秒`;
        return `${seconds}秒`;
    }

    startStatsReporting() {
        setInterval(() => {
            const stats = this.getStats();
            logger.info('性能统计', stats);
        }, config.monitoring.statsInterval);
    }
}

const monitor = new PerformanceMonitor();

// 速率限制管理
class RateLimiter {
    constructor() {
        this.clients = new Map(); // clientIP -> { messages: [], banned: false, banUntil: null }
        this.cleanupInterval = setInterval(() => this.cleanup(), 60000); // 每分钟清理一次
    }

    checkRate(clientIP) {
        if (!config.security.enableRateLimit) return true;

        const now = Date.now();
        const client = this.clients.get(clientIP) || { messages: [], banned: false, banUntil: null };

        // 检查是否在封禁期
        if (client.banned && client.banUntil > now) {
            return false;
        }

        // 解除封禁
        if (client.banned && client.banUntil <= now) {
            client.banned = false;
            client.banUntil = null;
            client.messages = [];
            logger.info('客户端解除封禁', { clientIP });
        }

        // 清理过期消息记录
        client.messages = client.messages.filter(time => now - time < 60000);

        // 检查速率限制
        if (client.messages.length >= config.security.maxMessagesPerMinute) {
            client.banned = true;
            client.banUntil = now + config.security.banDuration;
            this.clients.set(clientIP, client);
            logger.warn('客户端因速率限制被封禁', {
                clientIP,
                messageCount: client.messages.length,
                banDuration: config.security.banDuration
            });
            return false;
        }

        // 记录消息时间
        client.messages.push(now);
        this.clients.set(clientIP, client);
        return true;
    }

    cleanup() {
        const now = Date.now();
        for (const [clientIP, client] of this.clients.entries()) {
            // 清理过期的消息记录和解除过期的封禁
            client.messages = client.messages.filter(time => now - time < 60000);

            if (client.banned && client.banUntil <= now) {
                client.banned = false;
                client.banUntil = null;
            }

            // 如果客户端没有近期活动，删除记录
            if (client.messages.length === 0 && !client.banned) {
                this.clients.delete(clientIP);
            }
        }
    }

    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
    }
}

const rateLimiter = new RateLimiter();

// 创建WebSocket服务器实例
const wss = new WebSocketServer({
    port: config.websocket.port,
    host: config.websocket.host,
    backlog: config.websocket.backlog,
    perMessageDeflate: {
        zlibDeflateOptions: {
            chunkSize: 1024,
            memLevel: 7,
            level: 3
        },
        threshold: 1024,
        concurrencyLimit: 10
    }
});

// 客户端连接管理
const clients = new Set();
let connectionCount = 0;

// 增强的消息广播函数
function broadcastMessage(senderWs, message, isBinary = false) {
    let broadcastCount = 0;
    let failedCount = 0;
    const startTime = Date.now();

    clients.forEach(client => {
        if (client !== senderWs && client.readyState === client.OPEN) {
            try {
                client.send(message, { binary: isBinary });
                broadcastCount++;
            } catch (error) {
                failedCount++;
                logger.error('消息发送失败', {
                    clientId: client.clientId,
                    error: error.message
                }, error);
                clients.delete(client); // 移除无效连接
                monitor.incrementErrors();
            }
        }
    });

    const duration = Date.now() - startTime;
    monitor.incrementMessages();

    logger.debug('消息广播完成', {
        broadcastCount,
        failedCount,
        duration,
        messageSize: message.length,
        isBinary
    });

    return { broadcastCount, failedCount, duration };
}

// 错误处理包装器
function safeExecute(fn, context = '未知操作') {
    return async (...args) => {
        try {
            return await fn(...args);
        } catch (error) {
            logger.error(`${context}执行失败`, { args }, error);
            monitor.incrementErrors();
            throw error;
        }
    };
}

// 输入验证和清理
function validateAndSanitizeMessage(data, clientIP) {
    try {
        // 检查消息大小
        if (data.length > config.server.messageMaxSize) {
            throw new Error(`消息过大: ${data.length} bytes, 最大允许: ${config.server.messageMaxSize} bytes`);
        }

        // 检查速率限制
        if (!rateLimiter.checkRate(clientIP)) {
            throw new Error('速率限制: 发送消息过于频繁');
        }

        // 基本的内容验证
        if (typeof data === 'string') {
            // 移除潜在的恶意字符
            const sanitized = data.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
            if (sanitized.length !== data.length) {
                logger.warn('消息包含非法字符，已清理', {
                    clientIP,
                    originalLength: data.length,
                    sanitizedLength: sanitized.length
                });
            }
            return sanitized;
        }

        return data;
    } catch (error) {
        logger.warn('消息验证失败', { clientIP, error: error.message });
        throw error;
    }
}

// 处理新的客户端连接
wss.on('connection', safeExecute(function connection(ws, req) {
    connectionCount++;
    const clientId = `client_${connectionCount}_${Date.now()}`;
    const clientIP = req.socket.remoteAddress || req.headers['x-forwarded-for'] || 'unknown';
    const userAgent = req.headers['user-agent'] || 'unknown';
    const connectTime = new Date();

    // 检查连接数限制
    if (clients.size >= config.server.maxConnections) {
        logger.warn('连接数已达上限，拒绝新连接', {
            clientIP,
            userAgent,
            maxConnections: config.server.maxConnections,
            currentConnections: clients.size
        });
        ws.close(1013, '服务器连接数已满');
        monitor.incrementErrors();
        return;
    }

    // 检查速率限制
    if (!rateLimiter.checkRate(clientIP)) {
        logger.warn('客户端因速率限制被拒绝连接', { clientIP, userAgent });
        ws.close(1008, '连接被限制');
        monitor.incrementErrors();
        return;
    }

    // 添加到客户端集合
    clients.add(ws);
    ws.clientId = clientId;
    ws.clientIP = clientIP;
    ws.userAgent = userAgent;
    ws.connectTime = connectTime;
    ws.isAlive = true;
    ws.messageCount = 0;
    ws.lastActivity = connectTime;

    monitor.incrementConnections();

    logger.info('新客户端连接', {
        clientId,
        clientIP,
        userAgent,
        totalClients: clients.size,
        connectTime: connectTime.toISOString()
    });

    // 发送欢迎消息
    const welcomeMessage = {
        type: 'welcome',
        message: '欢迎连接到智能标签管理系统',
        clientId: clientId,
        serverInfo: {
            name: config.app.name,
            version: config.app.version,
            maxConnections: config.server.maxConnections,
            heartbeatInterval: config.server.heartbeatInterval
        },
        timestamp: connectTime.toISOString()
    };

    try {
        ws.send(JSON.stringify(welcomeMessage));
    } catch (error) {
        logger.error('发送欢迎消息失败', { clientId, error: error.message }, error);
    }

    // 处理客户端消息
    ws.on('message', safeExecute(function message(data, isBinary) {
        const messageTime = new Date();
        ws.lastActivity = messageTime;
        ws.messageCount++;

        try {
            // 验证和清理消息
            const sanitizedData = validateAndSanitizeMessage(data, clientIP);

            logger.info('收到消息', {
                clientId,
                clientIP,
                messageSize: data.length,
                isBinary,
                messageCount: ws.messageCount,
                timestamp: messageTime.toISOString()
            });

            // 转发消息给其他所有客户端
            const result = broadcastMessage(ws, sanitizedData, isBinary);

            logger.info('消息转发完成', {
                clientId,
                broadcastCount: result.broadcastCount,
                failedCount: result.failedCount,
                duration: result.duration,
                totalClients: clients.size
            });

            // 如果有发送失败，通知发送者
            if (result.failedCount > 0) {
                ws.send(JSON.stringify({
                    type: 'warning',
                    message: `消息已发送，但有 ${result.failedCount} 个客户端接收失败`,
                    timestamp: messageTime.toISOString()
                }));
            }

        } catch (error) {
            logger.error('消息处理错误', {
                clientId,
                clientIP,
                error: error.message,
                messageSize: data.length
            }, error);

            // 发送错误响应给客户端
            try {
                ws.send(JSON.stringify({
                    type: 'error',
                    message: error.message,
                    timestamp: messageTime.toISOString()
                }));
            } catch (sendError) {
                logger.error('发送错误响应失败', { clientId, error: sendError.message });
            }

            monitor.incrementErrors();
        }
    }, '消息处理'));

    // 处理心跳检测
    ws.on('pong', function heartbeat() {
        ws.isAlive = true;
        ws.lastActivity = new Date();
        logger.debug('收到心跳响应', { clientId });
    });

    // 处理连接错误
    ws.on('error', safeExecute(function error(err) {
        logger.error('客户端连接错误', {
            clientId,
            clientIP,
            error: err.message,
            connectDuration: Date.now() - ws.connectTime.getTime()
        }, err);
        monitor.incrementErrors();
    }, '连接错误处理'));

    // 处理连接关闭
    ws.on('close', safeExecute(function close(code, reason) {
        const disconnectTime = new Date();
        const connectDuration = disconnectTime.getTime() - ws.connectTime.getTime();

        clients.delete(ws);
        monitor.decrementConnections();

        logger.info('客户端断开连接', {
            clientId,
            clientIP,
            code,
            reason: reason.toString(),
            connectDuration,
            messageCount: ws.messageCount,
            remainingClients: clients.size,
            disconnectTime: disconnectTime.toISOString()
        });
    }, '连接关闭处理'));
}, '客户端连接处理'));

// 心跳检测定时器
const heartbeatInterval = setInterval(function ping() {
    const deadConnections = [];

    wss.clients.forEach(function each(ws) {
        if (ws.isAlive === false) {
            logger.info('移除无响应连接', {
                clientId: ws.clientId,
                clientIP: ws.clientIP,
                connectDuration: Date.now() - ws.connectTime.getTime()
            });
            clients.delete(ws);
            deadConnections.push(ws.clientId);
            monitor.decrementConnections();
            return ws.terminate();
        }

        ws.isAlive = false;
        try {
            ws.ping();
        } catch (error) {
            logger.error('发送心跳失败', { clientId: ws.clientId, error: error.message });
        }
    });

    if (deadConnections.length > 0) {
        logger.info('心跳检测完成', {
            removedConnections: deadConnections.length,
            activeConnections: clients.size
        });
    }
}, config.server.heartbeatInterval);

// 健康检查端点（如果启用）
if (config.monitoring.enableHealthCheck) {
    const healthCheckInterval = setInterval(() => {
        const stats = monitor.getStats();
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: stats.uptime,
            connections: stats.currentConnections,
            memory: stats.memory,
            errors: stats.totalErrors
        };

        // 检查健康状态
        if (stats.totalErrors > 100 || stats.currentConnections > config.server.maxConnections * 0.9) {
            health.status = 'warning';
        }

        logger.debug('健康检查', health);
    }, 30000); // 每30秒检查一次
}

// 服务器关闭处理
wss.on('close', function close() {
    clearInterval(heartbeatInterval);
    rateLimiter.destroy();
    logger.info('WebSocket服务器已关闭');
});

// 错误处理
wss.on('error', function error(err) {
    logger.error('WebSocket服务器错误', { error: err.message }, err);
    monitor.incrementErrors();
});

// 服务器启动成功
wss.on('listening', function listening() {
    const serverInfo = {
        host: config.websocket.host,
        port: config.websocket.port,
        maxConnections: config.server.maxConnections,
        heartbeatInterval: config.server.heartbeatInterval,
        environment: process.env.NODE_ENV || 'production',
        nodeVersion: process.version,
        startTime: new Date().toISOString()
    };

    logger.info('WebSocket服务器启动成功', serverInfo);

    console.log(`\n🚀 ${config.app.name} v${config.app.version}`);
    console.log(`📡 服务器运行在: ws://${config.websocket.host}:${config.websocket.port}`);
    console.log(`📊 最大连接数: ${config.server.maxConnections}`);
    console.log(`💓 心跳间隔: ${config.server.heartbeatInterval}ms`);
    console.log(`📝 日志级别: ${config.logging.level}`);
    console.log(`🔒 速率限制: ${config.security.enableRateLimit ? '启用' : '禁用'}`);
    console.log(`⚡ 状态: 等待客户端连接...\n`);
});

// 优雅关闭处理
const gracefulShutdown = (signal) => {
    logger.info(`收到${signal}信号，开始优雅关闭服务器...`);

    const shutdownStart = Date.now();
    clearInterval(heartbeatInterval);

    // 通知所有客户端服务器即将关闭
    const shutdownMessage = JSON.stringify({
        type: 'server_shutdown',
        message: '服务器即将关闭，请保存您的工作',
        timestamp: new Date().toISOString()
    });

    let notifiedClients = 0;
    clients.forEach(client => {
        if (client.readyState === client.OPEN) {
            try {
                client.send(shutdownMessage);
                notifiedClients++;
            } catch (error) {
                logger.error('通知客户端关闭失败', {
                    clientId: client.clientId,
                    error: error.message
                });
            }
        }
    });

    logger.info('已通知客户端服务器关闭', { notifiedClients });

    // 等待一段时间让客户端处理关闭通知
    setTimeout(() => {
        // 关闭服务器
        wss.close(() => {
            const shutdownDuration = Date.now() - shutdownStart;
            const finalStats = monitor.getStats();

            logger.info('服务器已安全关闭', {
                shutdownDuration,
                finalStats: {
                    totalConnections: finalStats.totalConnections,
                    totalMessages: finalStats.totalMessages,
                    totalErrors: finalStats.totalErrors,
                    uptime: finalStats.uptimeFormatted
                }
            });

            // 清理资源
            rateLimiter.destroy();

            console.log('\n👋 服务器已安全关闭');
            process.exit(0);
        });
    }, 2000); // 等待2秒

    // 强制关闭超时
    setTimeout(() => {
        logger.error('优雅关闭超时，强制退出');
        process.exit(1);
    }, config.server.gracefulShutdownTimeout);
};

// 监听关闭信号
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// 未捕获异常处理
process.on('uncaughtException', (error) => {
    logger.error('未捕获的异常', { error: error.message, stack: error.stack }, error);
    gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('未处理的Promise拒绝', { reason, promise });
    monitor.incrementErrors();
});

module.exports = { wss, logger, monitor, config };

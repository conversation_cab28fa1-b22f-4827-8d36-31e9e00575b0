// 整合的HTTP和WebSocket服务器
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const { WebSocketServer } = require('ws');

// 注释掉backend配置导入，避免端口冲突
// const backendConfig = require('./backend/config');

// 服务器配置
const config = {
    port: process.env.PORT || 8080, // 统一端口
    host: process.env.HOST || '0.0.0.0', // 监听所有接口
    frontendDir: path.join(__dirname, 'frontend') // 前端文件目录
};

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html; charset=utf-8',
    '.css': 'text/css; charset=utf-8',
    '.js': 'application/javascript; charset=utf-8',
    '.json': 'application/json; charset=utf-8',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.txt': 'text/plain; charset=utf-8'
};

// 获取文件MIME类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

// 发送文件响应
function sendFile(res, filePath) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            console.error('读取文件失败:', filePath, err.message);
            res.writeHead(500, { 'Content-Type': 'text/plain; charset=utf-8' });
            res.end('500 - 服务器内部错误');
            return;
        }
        
        const mimeType = getMimeType(filePath);
        res.writeHead(200, { 
            'Content-Type': mimeType,
            'Cache-Control': 'no-cache' // 开发时禁用缓存
        });
        res.end(data);
    });
}

// 发送404响应
function send404(res) {
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>404 - 页面未找到</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                h1 { color: #e74c3c; }
                a { color: #3498db; text-decoration: none; }
                a:hover { text-decoration: underline; }
            </style>
        </head>
        <body>
            <h1>404 - 页面未找到</h1>
            <p>您访问的页面不存在</p>
            <a href="/">返回首页</a>
        </body>
        </html>
    `);
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // 记录请求
    console.log(`[${new Date().toISOString()}] HTTP ${req.method} ${pathname}`);
    
    // 处理根路径，重定向到index.html
    if (pathname === '/') {
        pathname = '/index.html';
    }
    
    // 构建文件路径
    const filePath = path.join(config.frontendDir, pathname);
    
    // 安全检查：确保文件在frontend目录内
    const normalizedPath = path.normalize(filePath);
    if (!normalizedPath.startsWith(config.frontendDir)) {
        console.warn('安全警告: 尝试访问目录外文件:', pathname);
        send404(res);
        return;
    }
    
    // 检查文件是否存在
    fs.stat(filePath, (err, stats) => {
        if (err || !stats.isFile()) {
            send404(res);
            return;
        }
        
        sendFile(res, filePath);
    });
});

// 创建WebSocket服务器，附加到HTTP服务器
const wss = new WebSocketServer({ 
    server,
    perMessageDeflate: {
        zlibDeflateOptions: {
            chunkSize: 1024,
            memLevel: 7,
            level: 3
        },
        threshold: 1024,
        concurrencyLimit: 10
    }
});

// 使用简化的WebSocket逻辑，避免导入冲突
console.log('使用内置WebSocket逻辑');
// 简化的WebSocket逻辑
const clients = new Set();
    
    wss.on('connection', function connection(ws, req) {
        const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const clientIP = req.socket.remoteAddress;

        clients.add(ws);
        ws.clientId = clientId;

        console.log(`[${new Date().toISOString()}] WebSocket 新客户端连接: ${clientId} (${clientIP})`);
        console.log(`[${new Date().toISOString()}] 当前连接数: ${clients.size}`);

        // 发送欢迎消息
        ws.send(JSON.stringify({
            type: 'welcome',
            message: '欢迎连接到智能标签管理系统',
            clientId: clientId,
            timestamp: new Date().toISOString()
        }));
        
        // 处理消息
        ws.on('message', function message(data, isBinary) {
            console.log(`[${new Date().toISOString()}] 收到消息 ${clientId}: ${data.length} bytes`);
            console.log(`[${new Date().toISOString()}] 消息内容: ${data.toString()}`);
            console.log(`[${new Date().toISOString()}] 当前总连接数: ${clients.size}`);

            let forwardCount = 0;
            // 转发给其他客户端
            clients.forEach(client => {
                if (client !== ws && client.readyState === client.OPEN) {
                    try {
                        client.send(data, { binary: isBinary });
                        forwardCount++;
                        console.log(`[${new Date().toISOString()}] 消息已转发给客户端: ${client.clientId}`);
                    } catch (error) {
                        console.error('消息发送失败:', error.message);
                        clients.delete(client);
                    }
                }
            });

            console.log(`[${new Date().toISOString()}] 消息转发完成，转发给 ${forwardCount} 个客户端`);
        });
        
        // 处理断开连接
        ws.on('close', function close() {
            clients.delete(ws);
            console.log(`[${new Date().toISOString()}] WebSocket 客户端断开: ${clientId}`);
            console.log(`[${new Date().toISOString()}] 剩余连接数: ${clients.size}`);
        });
        
        // 处理错误
        ws.on('error', function error(err) {
            console.error(`[${new Date().toISOString()}] WebSocket 错误 ${clientId}:`, err.message);
        });
    });

// 启动服务器
server.listen(config.port, config.host, () => {
    console.log('\n🚀 智能标签管理系统 - 整合服务器启动成功');
    console.log('='.repeat(60));
    console.log(`📡 服务器地址: ${config.host}:${config.port}`);
    console.log('🌐 可用访问地址:');
    console.log(`   • http://localhost:${config.port} (前端界面)`);
    console.log(`   • http://127.0.0.1:${config.port} (前端界面)`);
    console.log(`   • ws://localhost:${config.port} (WebSocket)`);
    console.log(`   • ws://127.0.0.1:${config.port} (WebSocket)`);
    
    // 如果绑定到0.0.0.0，显示本机IP地址
    if (config.host === '0.0.0.0') {
        const os = require('os');
        const interfaces = os.networkInterfaces();
        for (const name of Object.keys(interfaces)) {
            for (const iface of interfaces[name]) {
                if (iface.family === 'IPv4' && !iface.internal) {
                    console.log(`   • http://${iface.address}:${config.port} (局域网)`);
                    console.log(`   • ws://${iface.address}:${config.port} (局域网WebSocket)`);
                }
            }
        }
    }
    
    console.log('='.repeat(60));
    console.log('💡 使用说明:');
    console.log('   1. 在浏览器中打开 HTTP 地址访问前端界面');
    console.log('   2. 前端会自动连接到同端口的 WebSocket 服务');
    console.log('   3. 支持多客户端实时通信');
    console.log('='.repeat(60));
    console.log('⚠️  按 Ctrl+C 停止服务器\n');
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${config.port} 已被占用，请尝试其他端口`);
        console.error(`   可以设置环境变量: PORT=8081`);
    } else {
        console.error('❌ 服务器错误:', err.message);
    }
    process.exit(1);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n📴 收到关闭信号，正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已安全关闭');
        process.exit(0);
    });
});

module.exports = server;

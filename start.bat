@echo off
chcp 65001 >nul
title 智能标签管理系统

echo.
echo ========================================
echo    智能标签管理系统启动脚本
echo ========================================
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过
echo.

echo 正在检查项目依赖...
if not exist "node_modules" (
    echo 📦 正在安装依赖包...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 项目依赖检查完成
echo.

echo 🚀 启动智能标签管理系统...
echo.
echo 💡 使用说明:
echo    - WebSocket服务器: ws://localhost:8080
echo    - 前端访问地址: http://localhost:3000
echo    - 按 Ctrl+C 停止所有服务器
echo.
echo ========================================
echo.

node start.js

echo.
echo 👋 系统已停止运行
pause

/* 智能标签管理系统 - 前端样式文件 */

/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

/* 主容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

/* 页面标题样式 */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    text-align: center;
    padding: 30px 20px;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* 通用section样式 */
section {
    padding: 30px;
    border-bottom: 1px solid #eee;
}

section:last-child {
    border-bottom: none;
}

section h2 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 连接配置区域样式 */
.connection-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.input-group input {
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.input-group input:focus {
    outline: none;
    border-color: #4facfe;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.input-hint {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
    line-height: 1.3;
}

.button-group {
    grid-column: 1 / -1;
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 10px;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover:not(:disabled) {
    background: #667eea;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 状态显示样式 */
.status-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 1px solid #e9ecef;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.status-label {
    font-weight: 600;
    color: #6c757d;
}

.status-value {
    font-weight: 700;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-connected {
    background: #d4edda;
    color: #155724;
}

.status-disconnected {
    background: #f8d7da;
    color: #721c24;
}

.status-connecting {
    background: #fff3cd;
    color: #856404;
}

/* 消息区域样式 */
.message-display {
    margin-bottom: 20px;
}

.message-container {
    height: 400px;
    border: 2px solid #e1e8ed;
    border-radius: 15px;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    font-family: 'Consolas', 'Monaco', monospace;
}

.welcome-message {
    text-align: center;
    color: #6c757d;
    padding: 50px 20px;
}

.welcome-message p {
    margin-bottom: 10px;
}

.message-item {
    margin-bottom: 15px;
    padding: 12px 16px;
    border-radius: 10px;
    word-wrap: break-word;
}

.message-sent {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    margin-left: 20px;
}

.message-received {
    background: #f3e5f5;
    border-left: 4px solid #9c27b0;
    margin-right: 20px;
}

.message-system {
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    font-style: italic;
    text-align: center;
}

.message-timestamp {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 5px;
}

/* 消息输入区域样式 */
.input-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

#messageInput {
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 15px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

#messageInput:focus {
    outline: none;
    border-color: #4facfe;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.input-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* 系统信息区域样式 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.info-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease;
}

.info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.info-label {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.info-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

/* 帮助区域样式 */
.help-content ol {
    padding-left: 20px;
    margin-bottom: 20px;
}

.help-content li {
    margin-bottom: 10px;
    line-height: 1.8;
}

.tips {
    background: #e8f5e8;
    padding: 15px 20px;
    border-radius: 10px;
    border-left: 4px solid #28a745;
    margin-top: 20px;
}

.tips p {
    margin: 0;
    color: #155724;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        border-radius: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    section {
        padding: 20px;
    }
    
    .connection-form {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .button-group {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
    }
    
    .status-display {
        grid-template-columns: 1fr;
    }
    
    .message-container {
        height: 300px;
    }
    
    .input-actions {
        flex-direction: column;
    }
    
    .info-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    section {
        padding: 15px;
    }
}

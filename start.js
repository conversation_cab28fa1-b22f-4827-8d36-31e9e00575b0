// 启动脚本 - 启动整合服务器
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 智能标签管理系统启动脚本');
console.log('正在启动整合服务器（HTTP + WebSocket）...\n');

// 配置
const config = {
    script: 'server.js',
    port: process.env.PORT || 8080,
    name: '整合服务器'
};

// 直接启动整合服务器
console.log('🔧 启动整合服务器...');
const serverProcess = spawn('node', [config.script], {
    stdio: 'inherit', // 直接继承父进程的输入输出
    cwd: __dirname
});

serverProcess.on('close', (code) => {
    if (code !== 0) {
        console.error(`\n❌ 服务器退出，代码: ${code}`);
    } else {
        console.log(`\n✅ 服务器正常退出`);
    }
    process.exit(code);
});

serverProcess.on('error', (err) => {
    console.error(`\n❌ 服务器启动失败: ${err.message}`);
    process.exit(1);
});

// 优雅关闭处理
const gracefulShutdown = () => {
    console.log('\n📴 收到关闭信号，正在停止服务器...');
    serverProcess.kill('SIGINT');
};

process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

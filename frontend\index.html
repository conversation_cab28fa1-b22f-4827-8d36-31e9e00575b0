<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能零售标签系统 - 管理平台</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Leaflet地图API -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 左侧导航栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="picture/logo.png" alt="系统Logo" class="logo-img">
                    <h3>智能零售标签系统</h3>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-item active" data-page="home">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </li>
                <li class="nav-item" data-page="terminal">
                    <i class="fas fa-terminal"></i>
                    <span>终端页面</span>
                </li>
                <li class="nav-item" data-page="display">
                    <i class="fas fa-desktop"></i>
                    <span>显示调整</span>
                </li>
                <li class="nav-item" data-page="location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>定位界面</span>
                </li>
                <li class="nav-item" data-page="ai">
                    <i class="fas fa-robot"></i>
                    <span>AI调控</span>
                </li>
            </ul>

            <!-- WebSocket连接状态 -->
            <div class="connection-status">
                <div class="status-indicator">
                    <span class="status-dot" id="connectionDot"></span>
                    <span class="status-text" id="connectionText">未连接</span>
                </div>
                <button class="connect-btn" id="connectBtn">连接系统</button>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 首页内容 -->
            <div class="page-content active" id="home-page">
                <header class="page-header">
                    <div class="header-content">
                        <img src="picture/logo.png" alt="智能零售标签系统" class="header-logo">
                        <div class="header-text">
                            <h1>智能零售标签系统</h1>
                            <p class="header-subtitle">基于CH32V307芯片的新一代零售价格管理解决方案</p>
                        </div>
                    </div>
                </header>

                <!-- 系统概览卡片 -->
                <div class="overview-cards">
                    <div class="card">
                        <div class="card-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="card-content">
                            <h3>中枢系统</h3>
                            <p>基于CH32V307芯片，内置GD25Q40 Flash文件系统与Shell操作环境</p>
                            <div class="card-stats">
                                <span class="stat-item">
                                    <i class="fas fa-wifi"></i>
                                    <span>Zigbee组网</span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="card-content">
                            <h3>终端设备</h3>
                            <p>低功耗墨水屏标签，长期稳定显示价格及促销信息</p>
                            <div class="card-stats">
                                <span class="stat-item">
                                    <i class="fas fa-battery-full"></i>
                                    <span>低功耗设计</span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="card-content">
                            <h3>AI智能调控</h3>
                            <p>根据市场行情、库存水平及促销策略自动优化价格</p>
                            <div class="card-stats">
                                <span class="stat-item">
                                    <i class="fas fa-chart-line"></i>
                                    <span>动态定价</span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-icon">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="card-content">
                            <h3>云端管理</h3>
                            <p>网页界面远程监控，实时查看设备状态和定位信息</p>
                            <div class="card-stats">
                                <span class="stat-item">
                                    <i class="fas fa-globe"></i>
                                    <span>远程控制</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统特性介绍 -->
                <div class="features-section">
                    <h2>系统核心特性</h2>
                    <div class="features-grid">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <h4>高效价格管理</h4>
                            <p>解决传统零售场景中价格调整滞后、促销推送效率低等痛点</p>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <h4>Zigbee无线组网</h4>
                            <p>通过Zigbee技术实现终端墨水屏标签的远程批量管理</p>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <h4>实时状态监控</h4>
                            <p>本地屏幕实时展示系统状态，网页界面远程监控网络运行</p>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h4>AI智能定价</h4>
                            <p>集成AI算法，根据市场行情自动优化价格，实现动态化定价</p>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <h4>节能环保</h4>
                            <p>墨水屏技术，超低功耗，长期稳定显示，绿色环保</p>
                        </div>

                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </div>
                            <h4>可扩展架构</h4>
                            <p>模块化设计，具备良好的可扩展性和易维护性</p>
                        </div>
                    </div>
                </div>

                <!-- 系统架构图 -->
                <div class="architecture-section">
                    <h2>系统架构</h2>
                    <div class="architecture-diagram">
                        <div class="arch-layer">
                            <div class="arch-item cloud">
                                <i class="fas fa-cloud"></i>
                                <span>云端服务</span>
                                <small>数据分析 | AI算法</small>
                            </div>
                        </div>

                        <div class="arch-layer">
                            <div class="arch-item web">
                                <i class="fas fa-laptop"></i>
                                <span>网页管理平台</span>
                                <small>远程监控 | 批量管理</small>
                            </div>
                        </div>

                        <div class="arch-layer">
                            <div class="arch-item hub">
                                <i class="fas fa-microchip"></i>
                                <span>中枢系统</span>
                                <small>CH32V307 | GD25Q40 Flash</small>
                            </div>
                        </div>

                        <div class="arch-layer">
                            <div class="arch-item terminals">
                                <div class="terminal-group">
                                    <div class="terminal-item">
                                        <i class="fas fa-tag"></i>
                                        <span>终端1</span>
                                    </div>
                                    <div class="terminal-item">
                                        <i class="fas fa-tag"></i>
                                        <span>终端2</span>
                                    </div>
                                    <div class="terminal-item">
                                        <i class="fas fa-tag"></i>
                                        <span>终端N</span>
                                    </div>
                                </div>
                                <small>墨水屏标签 | Zigbee通信</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 终端页面 -->
            <div class="page-content" id="terminal-page">
                <div class="page-header">
                    <h1><i class="fas fa-terminal"></i> 终端页面</h1>
                    <p>WebSocket通信终端，实时消息交互</p>
                </div>

                <!-- 服务器连接配置 -->
                <div class="terminal-config">
                    <div class="config-group">
                        <label for="serverAddress">服务器地址:</label>
                        <input type="text" id="serverAddress" placeholder="ws://websocket.tlmlab.cn/ 或 localhost:8082" value="localhost:8082">
                    </div>
                    <div class="config-actions">
                        <button id="disconnectBtn" class="btn btn-secondary" disabled>断开连接</button>
                    </div>
                </div>

                <!-- 连接状态显示 -->
                <div class="status-panel">
                    <div class="status-item">
                        <span class="status-label">连接状态:</span>
                        <span id="connectionStatus" class="status-value status-disconnected">未连接</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">服务器地址:</span>
                        <span id="serverAddressDisplay" class="status-value">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">客户端ID:</span>
                        <span id="clientId" class="status-value">-</span>
                    </div>
                </div>

                <!-- 消息交互区域 -->
                <div class="message-section">
                    <div class="message-display">
                        <div id="messageContainer" class="message-container">
                            <div class="welcome-message">
                                <p>👋 欢迎使用智能标签管理系统终端！</p>
                                <p>请先连接到WebSocket服务器开始使用。</p>
                            </div>
                        </div>
                    </div>

                    <div class="message-input">
                        <div class="input-container">
                            <textarea id="messageInput" placeholder="请输入要发送的消息..." rows="3" disabled></textarea>
                            <div class="input-actions">
                                <button id="clearBtn" class="btn btn-outline">清空消息</button>
                                <button id="sendBtn" class="btn btn-primary" disabled>发送消息</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统统计 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-label">发送消息数:</span>
                        <span id="sentCount" class="stat-value">0</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-label">接收消息数:</span>
                        <span id="receivedCount" class="stat-value">0</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-label">连接时长:</span>
                        <span id="connectionTime" class="stat-value">00:00:00</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-label">最后活动:</span>
                        <span id="lastActivity" class="stat-value">-</span>
                    </div>
                </div>
            </div>

            <!-- 显示调整界面 -->
            <div class="page-content" id="display-page">
                <div class="page-header">
                    <h1><i class="fas fa-desktop"></i> 显示调整界面</h1>
                    <p>墨水屏显示参数配置与调整</p>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-tools"></i>
                    <h3>功能开发中</h3>
                    <p>显示调整功能正在开发中，敬请期待...</p>
                </div>
            </div>

            <!-- 定位界面 -->
            <div class="page-content" id="location-page">
                <div class="page-header">
                    <h1><i class="fas fa-map-marker-alt"></i> 定位界面</h1>
                    <p>设备位置信息与网络拓扑</p>
                </div>

                <!-- 地图控制面板 -->
                <div class="map-control-panel">
                    <div class="control-section">
                        <h3><i class="fas fa-satellite"></i> 地图控制</h3>
                        <div class="control-buttons">
                            <button class="btn btn-primary" id="centerMapBtn">
                                <i class="fas fa-crosshairs"></i> 居中显示
                            </button>
                            <button class="btn btn-outline" id="clearMarkersBtn">
                                <i class="fas fa-trash"></i> 清除标记
                            </button>
                            <button class="btn btn-outline" id="refreshMapBtn">
                                <i class="fas fa-sync"></i> 刷新地图
                            </button>
                        </div>
                    </div>

                    <div class="control-section">
                        <h3><i class="fas fa-info-circle"></i> 位置统计</h3>
                        <div class="location-stats">
                            <div class="stat-item">
                                <span class="stat-label">在线设备:</span>
                                <span class="stat-value" id="onlineDevices">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">总标记点:</span>
                                <span class="stat-value" id="totalMarkers">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">最后更新:</span>
                                <span class="stat-value" id="lastLocationUpdate">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地图容器 -->
                <div class="map-container">
                    <div id="mapContainer" class="map-display">
                        <div class="map-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>正在加载地图...</p>
                        </div>
                    </div>
                </div>

                <!-- 设备列表 -->
                <div class="device-list-panel">
                    <h3><i class="fas fa-list"></i> 设备列表</h3>
                    <div class="device-list" id="deviceList">
                        <div class="no-devices">
                            <i class="fas fa-map-marker-alt"></i>
                            <p>暂无设备位置信息</p>
                            <small>等待接收位置数据...</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI调控界面 -->
            <div class="page-content" id="ai-page">
                <div class="page-header">
                    <h1><i class="fas fa-robot"></i> AI调控界面</h1>
                    <p>智能价格调控与策略管理</p>
                </div>

                <!-- AI配置面板 -->
                <div class="ai-config-panel">
                    <div class="config-section">
                        <h3><i class="fas fa-cog"></i> AI配置</h3>
                        <div class="config-form">
                            <div class="input-group">
                                <label for="apiKey">API密钥:</label>
                                <input type="password" id="apiKey" placeholder="请输入GLM-4 API密钥">
                                <small class="input-hint">您的API密钥将安全存储在本地</small>
                            </div>
                            <div class="input-group">
                                <label for="aiModel">模型选择:</label>
                                <select id="aiModel">
                                    <option value="glm-4-plus">GLM-4-Plus (推荐)</option>
                                    <option value="glm-4-air-250414">GLM-4-Air</option>
                                    <option value="glm-4-airx">GLM-4-AirX</option>
                                    <option value="glm-4-flashx">GLM-4-FlashX</option>
                                </select>
                            </div>
                            <div class="config-actions">
                                <button class="btn btn-primary" id="saveConfigBtn">
                                    <i class="fas fa-save"></i> 保存配置
                                </button>
                                <button class="btn btn-outline" id="testConnectionBtn">
                                    <i class="fas fa-plug"></i> 测试连接
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <h3><i class="fas fa-chart-line"></i> 调控状态</h3>
                        <div class="ai-status">
                            <div class="status-item">
                                <span class="status-label">API状态:</span>
                                <span class="status-value" id="apiStatus">未配置</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">当前模型:</span>
                                <span class="status-value" id="currentModel">-</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">调用次数:</span>
                                <span class="status-value" id="apiCallCount">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI对话区域 -->
                <div class="ai-chat-container">
                    <div class="chat-header">
                        <h3><i class="fas fa-comments"></i> AI智能助手</h3>
                        <div class="chat-controls">
                            <button class="btn btn-outline" id="clearChatBtn">
                                <i class="fas fa-trash"></i> 清空对话
                            </button>
                            <button class="btn btn-outline" id="exportChatBtn">
                                <i class="fas fa-download"></i> 导出对话
                            </button>
                        </div>
                    </div>

                    <div class="chat-messages" id="chatMessages">
                        <div class="welcome-message">
                            <div class="ai-avatar">🤖</div>
                            <div class="message-content">
                                <h4>欢迎使用AI智能调控助手！</h4>
                                <p>我可以帮助您：</p>
                                <ul>
                                    <li>📊 分析市场价格趋势</li>
                                    <li>💰 制定动态定价策略</li>
                                    <li>📈 优化促销活动方案</li>
                                    <li>🎯 提供库存管理建议</li>
                                    <li>💡 解答零售管理问题</li>
                                </ul>
                                <p><strong>请先配置API密钥，然后开始对话。</strong></p>
                            </div>
                        </div>
                    </div>

                    <div class="chat-input-area">
                        <div class="input-container">
                            <textarea id="chatInput" placeholder="请输入您的问题或需求..." rows="3" disabled></textarea>
                            <div class="input-actions">
                                <button class="btn btn-outline" id="voiceInputBtn" disabled>
                                    <i class="fas fa-microphone"></i>
                                </button>
                                <button class="btn btn-primary" id="sendChatBtn" disabled>
                                    <i class="fas fa-paper-plane"></i> 发送
                                </button>
                            </div>
                        </div>

                        <!-- 快捷问题 -->
                        <div class="quick-questions">
                            <span class="quick-label">快捷问题:</span>
                            <button class="quick-btn" data-question="如何制定动态定价策略？">动态定价</button>
                            <button class="quick-btn" data-question="分析当前市场价格趋势">价格分析</button>
                            <button class="quick-btn" data-question="优化库存管理策略">库存优化</button>
                            <button class="quick-btn" data-question="设计促销活动方案">促销策略</button>
                        </div>
                    </div>
                </div>

                <!-- AI分析结果展示 -->
                <div class="ai-analysis-panel">
                    <h3><i class="fas fa-brain"></i> AI分析结果</h3>
                    <div class="analysis-content" id="analysisContent">
                        <div class="no-analysis">
                            <i class="fas fa-chart-bar"></i>
                            <p>暂无分析结果</p>
                            <small>与AI助手对话后，相关分析结果将在此显示</small>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 连接配置弹窗 -->
    <div class="modal" id="connectionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>连接系统</h3>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body">
                <div class="input-group">
                    <label for="modalServerAddress">服务器地址:</label>
                    <input type="text" id="modalServerAddress" placeholder="ws://websocket.tlmlab.cn/ 或 localhost:8082" value="localhost:8082">
                    <small class="input-hint">支持完整WebSocket URL或主机名:端口格式</small>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modalCancel">取消</button>
                <button class="btn btn-primary" id="modalConnect">连接</button>
            </div>
        </div>
    </div>

    <!-- 引入配置文件和脚本文件 -->
    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>

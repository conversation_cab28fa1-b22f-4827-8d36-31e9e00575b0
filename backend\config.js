// 后端统一配置文件
const path = require('path');

// 配置验证函数
function validateConfig(config) {
    const errors = [];

    // 验证端口号
    if (config.websocket.port < 1 || config.websocket.port > 65535) {
        errors.push(`无效的端口号: ${config.websocket.port}`);
    }

    // 验证最大连接数
    if (config.server.maxConnections < 1 || config.server.maxConnections > 10000) {
        errors.push(`无效的最大连接数: ${config.server.maxConnections}`);
    }

    // 验证心跳间隔
    if (config.server.heartbeatInterval < 1000 || config.server.heartbeatInterval > 300000) {
        errors.push(`无效的心跳间隔: ${config.server.heartbeatInterval}`);
    }

    // 验证消息大小限制
    if (config.server.messageMaxSize < 1024 || config.server.messageMaxSize > 100 * 1024 * 1024) {
        errors.push(`无效的消息大小限制: ${config.server.messageMaxSize}`);
    }

    if (errors.length > 0) {
        throw new Error('配置验证失败:\n' + errors.join('\n'));
    }

    return true;
}

// 环境变量解析函数
function parseEnvInt(envVar, defaultValue, min = 0, max = Infinity) {
    const value = process.env[envVar];
    if (!value) return defaultValue;

    const parsed = parseInt(value, 10);
    if (isNaN(parsed) || parsed < min || parsed > max) {
        console.warn(`警告: 环境变量 ${envVar} 值无效 (${value})，使用默认值 ${defaultValue}`);
        return defaultValue;
    }

    return parsed;
}

function parseEnvBool(envVar, defaultValue) {
    const value = process.env[envVar];
    if (!value) return defaultValue;

    return value.toLowerCase() === 'true' || value === '1';
}

// 主配置对象
const config = {
    // 应用信息
    app: {
        name: '智能标签管理系统',
        version: '1.0.0',
        description: 'WebSocket实时通信服务器',
        author: 'SmartTip Team'
    },

    // WebSocket服务器配置
    websocket: {
        port: parseEnvInt('WS_PORT', 8080, 1, 65535), // WebSocket服务器端口
        host: process.env.WS_HOST || 'localhost', // WebSocket服务器地址
        backlog: parseEnvInt('WS_BACKLOG', 511, 1, 65535) // 连接队列长度
    },

    // 服务器配置
    server: {
        maxConnections: parseEnvInt('MAX_CONNECTIONS', 100, 1, 10000), // 最大连接数
        heartbeatInterval: parseEnvInt('HEARTBEAT_INTERVAL', 30000, 1000, 300000), // 心跳检测间隔(毫秒)
        messageMaxSize: parseEnvInt('MESSAGE_MAX_SIZE', 1024 * 1024, 1024, 100 * 1024 * 1024), // 消息最大大小
        connectionTimeout: parseEnvInt('CONNECTION_TIMEOUT', 60000, 5000, 300000), // 连接超时时间
        gracefulShutdownTimeout: parseEnvInt('SHUTDOWN_TIMEOUT', 10000, 1000, 60000) // 优雅关闭超时时间
    },

    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || 'info', // 日志级别: debug, info, warn, error
        enableConsole: parseEnvBool('LOG_CONSOLE', true), // 是否启用控制台输出
        enableFile: parseEnvBool('LOG_FILE', false), // 是否启用文件日志
        logDir: process.env.LOG_DIR || path.join(__dirname, '../logs'), // 日志目录
        maxFileSize: parseEnvInt('LOG_MAX_SIZE', 10 * 1024 * 1024, 1024 * 1024, 100 * 1024 * 1024), // 日志文件最大大小
        maxFiles: parseEnvInt('LOG_MAX_FILES', 5, 1, 100), // 最大日志文件数
        datePattern: 'YYYY-MM-DD' // 日志文件日期格式
    },

    // 性能监控配置
    monitoring: {
        enableStats: parseEnvBool('ENABLE_STATS', true), // 是否启用性能统计
        statsInterval: parseEnvInt('STATS_INTERVAL', 60000, 10000, 300000), // 统计间隔
        enableHealthCheck: parseEnvBool('ENABLE_HEALTH_CHECK', true) // 是否启用健康检查
    },

    // 安全配置
    security: {
        enableRateLimit: parseEnvBool('ENABLE_RATE_LIMIT', true), // 是否启用速率限制
        maxMessagesPerMinute: parseEnvInt('MAX_MESSAGES_PER_MINUTE', 60, 1, 1000), // 每分钟最大消息数
        banDuration: parseEnvInt('BAN_DURATION', 300000, 60000, 3600000) // 封禁时长(毫秒)
    },

    // 开发环境配置
    development: {
        isDevelopment: process.env.NODE_ENV === 'development',
        enableDebug: parseEnvBool('ENABLE_DEBUG', process.env.NODE_ENV === 'development'),
        enableCors: parseEnvBool('ENABLE_CORS', true) // 是否启用CORS
    }
};

// 验证配置
try {
    validateConfig(config);
    console.log('✅ 配置验证通过');
} catch (error) {
    console.error('❌ 配置验证失败:', error.message);
    process.exit(1);
}

// 导出配置和验证函数
module.exports = {
    ...config,
    validateConfig,
    // 获取配置摘要
    getConfigSummary() {
        return {
            app: config.app.name + ' v' + config.app.version,
            server: `${config.websocket.host}:${config.websocket.port}`,
            maxConnections: config.server.maxConnections,
            environment: process.env.NODE_ENV || 'production',
            logLevel: config.logging.level
        };
    }
};

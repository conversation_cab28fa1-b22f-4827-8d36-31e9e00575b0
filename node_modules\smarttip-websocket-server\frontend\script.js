// 智能零售标签系统 - 前端脚本文件

class SmartRetailSystem {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
        this.reconnectTimer = null;
        this.connectionTimer = null;
        this.connectionStartTime = null;
        this.sentCount = 0;
        this.receivedCount = 0;
        this.connectedAddress = '';
        this.currentPage = 'home';
        
        this.initElements();
        this.bindEvents();
        this.loadConfig();
        this.initNavigation();
    }
    
    // 初始化DOM元素引用
    initElements() {
        this.elements = {
            // 导航元素
            navItems: document.querySelectorAll('.nav-item'),
            pageContents: document.querySelectorAll('.page-content'),
            
            // 连接相关元素
            connectBtn: document.getElementById('connectBtn'),
            disconnectBtn: document.getElementById('disconnectBtn'),
            connectionDot: document.getElementById('connectionDot'),
            connectionText: document.getElementById('connectionText'),
            connectionModal: document.getElementById('connectionModal'),
            modalClose: document.getElementById('modalClose'),
            modalCancel: document.getElementById('modalCancel'),
            modalConnect: document.getElementById('modalConnect'),
            modalServerAddress: document.getElementById('modalServerAddress'),
            
            // 终端页面元素
            serverAddress: document.getElementById('serverAddress'),
            connectionStatus: document.getElementById('connectionStatus'),
            serverAddressDisplay: document.getElementById('serverAddressDisplay'),
            clientId: document.getElementById('clientId'),
            messageContainer: document.getElementById('messageContainer'),
            messageInput: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            clearBtn: document.getElementById('clearBtn'),
            sentCount: document.getElementById('sentCount'),
            receivedCount: document.getElementById('receivedCount'),
            connectionTime: document.getElementById('connectionTime'),
            lastActivity: document.getElementById('lastActivity')
        };
    }
    
    // 绑定事件监听器
    bindEvents() {
        // 导航事件
        this.elements.navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const page = e.currentTarget.dataset.page;
                this.switchPage(page);
            });
        });
        
        // 连接按钮事件
        if (this.elements.connectBtn) {
            this.elements.connectBtn.addEventListener('click', () => this.showConnectionModal());
        }
        
        if (this.elements.disconnectBtn) {
            this.elements.disconnectBtn.addEventListener('click', () => this.disconnect());
        }
        
        // 模态框事件
        if (this.elements.modalClose) {
            this.elements.modalClose.addEventListener('click', () => this.hideConnectionModal());
        }
        
        if (this.elements.modalCancel) {
            this.elements.modalCancel.addEventListener('click', () => this.hideConnectionModal());
        }
        
        if (this.elements.modalConnect) {
            this.elements.modalConnect.addEventListener('click', () => this.connectFromModal());
        }
        
        // 消息发送事件
        if (this.elements.sendBtn) {
            this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        }
        
        if (this.elements.messageInput) {
            this.elements.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }
        
        // 清空消息事件
        if (this.elements.clearBtn) {
            this.elements.clearBtn.addEventListener('click', () => this.clearMessages());
        }
        
        // 模态框外部点击关闭
        if (this.elements.connectionModal) {
            this.elements.connectionModal.addEventListener('click', (e) => {
                if (e.target === this.elements.connectionModal) {
                    this.hideConnectionModal();
                }
            });
        }
    }
    
    // 初始化导航
    initNavigation() {
        this.switchPage('home');
    }
    
    // 切换页面
    switchPage(page) {
        // 更新导航状态
        this.elements.navItems.forEach(item => {
            item.classList.remove('active');
            if (item.dataset.page === page) {
                item.classList.add('active');
            }
        });
        
        // 更新页面内容
        this.elements.pageContents.forEach(content => {
            content.classList.remove('active');
            if (content.id === `${page}-page`) {
                content.classList.add('active');
            }
        });
        
        this.currentPage = page;
    }
    
    // 显示连接模态框
    showConnectionModal() {
        if (this.elements.connectionModal) {
            this.elements.connectionModal.classList.add('active');
            if (this.elements.modalServerAddress) {
                this.elements.modalServerAddress.focus();
            }
        }
    }
    
    // 隐藏连接模态框
    hideConnectionModal() {
        if (this.elements.connectionModal) {
            this.elements.connectionModal.classList.remove('active');
        }
    }
    
    // 从模态框连接
    connectFromModal() {
        const address = this.elements.modalServerAddress?.value.trim();
        if (address) {
            this.connect(address);
            this.hideConnectionModal();
        }
    }
    
    // 连接WebSocket服务器
    connect(address = null) {
        if (this.isConnected) return;
        
        const serverAddress = address || this.elements.serverAddress?.value.trim() || 'localhost:8082';
        
        // 构建WebSocket URL
        let url;
        if (serverAddress.startsWith('ws://') || serverAddress.startsWith('wss://')) {
            url = serverAddress;
        } else {
            url = `ws://${serverAddress}`;
        }
        
        this.connectedAddress = serverAddress;
        this.updateConnectionStatus('connecting', '连接中...');
        this.addSystemMessage(`正在连接到 ${url}...`);
        
        try {
            this.ws = new WebSocket(url);
            this.setupWebSocketEvents();
        } catch (error) {
            this.handleConnectionError('连接失败: ' + error.message);
        }
    }
    
    // 设置WebSocket事件监听
    setupWebSocketEvents() {
        this.ws.onopen = () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.connectionStartTime = new Date();
            this.startConnectionTimer();
            
            console.log('WebSocket连接成功:', this.connectedAddress);
            console.log('WebSocket URL:', this.ws.url);
            this.updateConnectionStatus('connected', '已连接');
            if (this.elements.serverAddressDisplay) {
                this.elements.serverAddressDisplay.textContent = this.connectedAddress;
            }
            this.addSystemMessage(`✅ 成功连接到服务器: ${this.connectedAddress}`);
            this.addSystemMessage(`🔗 实际连接URL: ${this.ws.url}`);
            this.updateUI();
        };
        
        this.ws.onmessage = (event) => {
            console.log('收到WebSocket消息:', event.data);
            this.handleMessage(event.data);
            this.updateLastActivity();
        };
        
        this.ws.onclose = (event) => {
            this.isConnected = false;
            this.clearConnectionTimer();
            this.updateConnectionStatus('disconnected', '连接已断开');
            this.addSystemMessage('❌ 连接已断开');
            this.updateUI();
            
            if (this.reconnectAttempts < this.maxReconnectAttempts && !event.wasClean) {
                this.scheduleReconnect();
            }
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.addSystemMessage('❌ 连接错误');
        };
    }
    
    // 断开连接
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, '用户主动断开');
            this.clearReconnectTimer();
        }
    }
    
    // 处理接收到的消息
    handleMessage(data) {
        this.receivedCount++;
        if (this.elements.receivedCount) {
            this.elements.receivedCount.textContent = this.receivedCount;
        }
        
        try {
            const message = JSON.parse(data);
            if (message.type === 'welcome') {
                this.clientId = message.clientId;
                if (this.elements.clientId) {
                    this.elements.clientId.textContent = this.clientId;
                }
                this.addSystemMessage(`🆔 客户端ID: ${this.clientId}`);
            } else {
                this.addReceivedMessage(data);
            }
        } catch (error) {
            this.addReceivedMessage(data);
        }
    }
    
    // 发送消息
    sendMessage() {
        if (!this.isConnected || !this.ws) {
            this.addSystemMessage('❌ 请先连接到服务器');
            return;
        }
        
        const message = this.elements.messageInput?.value.trim();
        if (!message) return;
        
        try {
            console.log('发送消息:', message, '到服务器:', this.connectedAddress);
            console.log('WebSocket状态:', this.ws.readyState);
            this.ws.send(message);
            this.sentCount++;
            if (this.elements.sentCount) {
                this.elements.sentCount.textContent = this.sentCount;
            }
            this.addSentMessage(message);
            if (this.elements.messageInput) {
                this.elements.messageInput.value = '';
            }
            this.updateLastActivity();
            console.log('消息发送成功');
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addSystemMessage('❌ 发送消息失败: ' + error.message);
        }
    }
    
    // 更新连接状态
    updateConnectionStatus(status, text) {
        if (this.elements.connectionDot) {
            this.elements.connectionDot.className = 'status-dot';
            if (status === 'connected') {
                this.elements.connectionDot.classList.add('connected');
            }
        }
        
        if (this.elements.connectionText) {
            this.elements.connectionText.textContent = text;
        }
        
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.className = `status-value status-${status}`;
            this.elements.connectionStatus.textContent = text;
        }
    }
    
    // 更新界面状态
    updateUI() {
        if (this.elements.connectBtn) {
            this.elements.connectBtn.textContent = this.isConnected ? '已连接' : '连接系统';
            this.elements.connectBtn.disabled = this.isConnected;
        }

        if (this.elements.disconnectBtn) {
            this.elements.disconnectBtn.disabled = !this.isConnected;
        }

        if (this.elements.messageInput) {
            this.elements.messageInput.disabled = !this.isConnected;
        }

        if (this.elements.sendBtn) {
            this.elements.sendBtn.disabled = !this.isConnected;
        }

        if (!this.isConnected) {
            if (this.elements.serverAddressDisplay) {
                this.elements.serverAddressDisplay.textContent = '-';
            }
            if (this.elements.clientId) {
                this.elements.clientId.textContent = '-';
            }
        }
    }

    // 添加系统消息
    addSystemMessage(message) {
        this.addMessage(message, 'system');
    }

    // 添加发送的消息
    addSentMessage(message) {
        this.addMessage(`我: ${message}`, 'sent');
    }

    // 添加接收的消息
    addReceivedMessage(message) {
        this.addMessage(`收到: ${message}`, 'received');
    }

    // 添加消息到容器
    addMessage(content, type) {
        if (!this.elements.messageContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message-item message-${type}`;

        const contentDiv = document.createElement('div');
        contentDiv.textContent = content;
        messageDiv.appendChild(contentDiv);

        const timestampDiv = document.createElement('div');
        timestampDiv.className = 'message-timestamp';
        timestampDiv.textContent = new Date().toLocaleTimeString();
        messageDiv.appendChild(timestampDiv);

        this.elements.messageContainer.appendChild(messageDiv);
        this.elements.messageContainer.scrollTop = this.elements.messageContainer.scrollHeight;
    }

    // 清空消息
    clearMessages() {
        if (this.elements.messageContainer) {
            this.elements.messageContainer.innerHTML = `
                <div class="welcome-message">
                    <p>👋 欢迎使用智能标签管理系统终端！</p>
                    <p>请先连接到WebSocket服务器开始使用。</p>
                </div>
            `;
        }
    }

    // 开始连接时长计时
    startConnectionTimer() {
        this.connectionTimer = setInterval(() => {
            if (this.connectionStartTime && this.elements.connectionTime) {
                const duration = Date.now() - this.connectionStartTime.getTime();
                this.elements.connectionTime.textContent = this.formatDuration(duration);
            }
        }, 1000);
    }

    // 清除连接计时器
    clearConnectionTimer() {
        if (this.connectionTimer) {
            clearInterval(this.connectionTimer);
            this.connectionTimer = null;
        }
    }

    // 更新最后活动时间
    updateLastActivity() {
        if (this.elements.lastActivity) {
            this.elements.lastActivity.textContent = new Date().toLocaleTimeString();
        }
    }

    // 格式化持续时间
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        const h = hours.toString().padStart(2, '0');
        const m = (minutes % 60).toString().padStart(2, '0');
        const s = (seconds % 60).toString().padStart(2, '0');

        return `${h}:${m}:${s}`;
    }

    // 处理连接错误
    handleConnectionError(message) {
        this.updateConnectionStatus('disconnected', '连接失败');
        this.addSystemMessage(`❌ ${message}`);
        this.updateUI();
    }

    // 安排重连
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectInterval * this.reconnectAttempts;

        this.addSystemMessage(`🔄 ${delay/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

        this.reconnectTimer = setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }

    // 清除重连定时器
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        this.reconnectAttempts = 0;
    }

    // 加载配置
    loadConfig() {
        try {
            const savedAddress = localStorage.getItem('smartRetail_serverAddress');
            if (savedAddress) {
                if (this.elements.serverAddress) {
                    this.elements.serverAddress.value = savedAddress;
                }
                if (this.elements.modalServerAddress) {
                    this.elements.modalServerAddress.value = savedAddress;
                }
            }
        } catch (error) {
            console.warn('加载配置失败:', error);
        }
    }

    // 保存配置
    saveConfig() {
        try {
            const address = this.elements.serverAddress?.value || this.elements.modalServerAddress?.value;
            if (address) {
                localStorage.setItem('smartRetail_serverAddress', address);
            }
        } catch (error) {
            console.warn('保存配置失败:', error);
        }
    }
}

// 页面加载完成后初始化系统
document.addEventListener('DOMContentLoaded', () => {
    window.smartRetailSystem = new SmartRetailSystem();
});

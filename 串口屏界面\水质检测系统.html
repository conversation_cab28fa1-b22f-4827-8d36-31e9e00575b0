<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水质检测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f0f5ff;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #1e88e5;
            color: white;
            padding: 20px 0;
            text-align: center;
            border-radius: 8px 8px 0 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-title {
            font-size: 1.2rem;
            color: #555;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .card-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #1e88e5;
        }
        .pollution-general {
            background-color: #ffeb3b;
            color: #333;
        }
        .ph-alkaline {
            background-color: #4caf50;
            color: white;
        }
        .arm-up {
            background-color: #2196f3;
            color: white;
        }
        footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.9rem;
        }
        .last-update {
            background-color: rgba(0,0,0,0.05);
            padding: 10px;
            border-radius: 4px;
            text-align: right;
            font-size: 0.9rem;
            color: #666;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>水质检测系统</h1>
            <p>实时监测与分析</p>
        </header>

        <div class="dashboard">
            <div class="card">
                <div class="card-title">水质污染程度</div>
                <div class="card-icon">
                    <i class="fas fa-water"></i>
                </div>
                <div class="card-value pollution-general">一般</div>
                <p>当前水质污染在可控范围内</p>
            </div>

            <div class="card">
                <div class="card-title">水质酸碱性</div>
                <div class="card-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <div class="card-value ph-alkaline">碱性</div>
                <p>pH值高于7.0，呈碱性状态</p>
            </div>

            <div class="card">
                <div class="card-title">机械臂状态</div>
                <div class="card-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="card-value arm-up">降落</div>
                <p>机械臂当前处于降落状态</p>
            </div>
        </div>

        <div class="last-update">
            <p>最后更新时间: <span id="update-time">2023-11-20 14:30:25</span></p>
        </div>

        <footer>
            <p>© 2023 水质检测系统 | 技术支持: 智能监测团队</p>
        </footer>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            
            document.getElementById('update-time').textContent = 
                `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
        
        // 初始更新时间
        updateTime();
        
        // 每秒更新一次时间
        setInterval(updateTime, 1000);
    </script>
</body>
</html>

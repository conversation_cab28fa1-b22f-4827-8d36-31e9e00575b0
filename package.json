{"name": "smarttip-websocket-server", "version": "1.0.0", "description": "智能标签管理系统WebSocket服务器", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "node backend/server.js", "frontend": "node frontend-server.js", "dev:frontend": "node frontend-server.js", "dev:all": "concurrently \"npm run dev\" \"npm run dev:frontend\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["websocket", "nodejs", "智能标签", "实时通信"], "author": "", "license": "MIT", "dependencies": {"smarttip-websocket-server": "file:", "ws": "^8.18.3"}, "devDependencies": {"concurrently": "^7.6.0"}, "engines": {"node": ">=14.0.0"}}
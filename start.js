// 启动脚本 - 同时启动前后端服务器
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 智能标签管理系统启动脚本');
console.log('正在启动前后端服务器...\n');

// 配置
const config = {
    backend: {
        script: 'backend/server.js',
        port: process.env.WS_PORT || 8080,
        name: 'WebSocket服务器'
    },
    frontend: {
        script: 'frontend-server.js',
        port: process.env.HTTP_PORT || 3000,
        name: 'HTTP服务器'
    }
};

// 启动进程的函数
function startProcess(scriptPath, name, color) {
    const process = spawn('node', [scriptPath], {
        stdio: 'pipe',
        cwd: __dirname
    });
    
    // 处理输出
    process.stdout.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            console.log(`${color}[${name}]${'\x1b[0m'} ${output}`);
        }
    });
    
    process.stderr.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            console.error(`${color}[${name}]${'\x1b[0m'} ${'\x1b[31m'}ERROR:${'\x1b[0m'} ${output}`);
        }
    });
    
    process.on('close', (code) => {
        if (code !== 0) {
            console.error(`${color}[${name}]${'\x1b[0m'} ${'\x1b[31m'}进程退出，代码: ${code}${'\x1b[0m'}`);
        } else {
            console.log(`${color}[${name}]${'\x1b[0m'} ${'\x1b[32m'}进程正常退出${'\x1b[0m'}`);
        }
    });
    
    process.on('error', (err) => {
        console.error(`${color}[${name}]${'\x1b[0m'} ${'\x1b[31m'}启动失败: ${err.message}${'\x1b[0m'}`);
    });
    
    return process;
}

// 启动后端服务器
console.log('🔧 启动WebSocket服务器...');
const backendProcess = startProcess(
    config.backend.script,
    config.backend.name,
    '\x1b[34m' // 蓝色
);

// 等待一秒后启动前端服务器
setTimeout(() => {
    console.log('🌐 启动HTTP服务器...');
    const frontendProcess = startProcess(
        config.frontend.script,
        config.frontend.name,
        '\x1b[32m' // 绿色
    );
    
    // 等待两秒后显示访问信息
    setTimeout(() => {
        console.log('\n' + '='.repeat(60));
        console.log('🎉 智能标签管理系统启动完成！');
        console.log('='.repeat(60));
        console.log(`📡 WebSocket服务器: ws://localhost:${config.backend.port}`);
        console.log(`🌐 前端访问地址: http://localhost:${config.frontend.port}`);
        console.log('='.repeat(60));
        console.log('💡 使用说明:');
        console.log('   1. 在浏览器中打开前端地址');
        console.log('   2. 输入WebSocket服务器地址和端口');
        console.log('   3. 点击连接开始使用');
        console.log('='.repeat(60));
        console.log('⚠️  按 Ctrl+C 停止所有服务器\n');
    }, 2000);
    
    // 优雅关闭处理
    const gracefulShutdown = () => {
        console.log('\n📴 收到关闭信号，正在停止所有服务器...');
        
        backendProcess.kill('SIGINT');
        frontendProcess.kill('SIGINT');
        
        setTimeout(() => {
            console.log('✅ 所有服务器已停止');
            process.exit(0);
        }, 2000);
    };
    
    process.on('SIGINT', gracefulShutdown);
    process.on('SIGTERM', gracefulShutdown);
    
}, 1000);

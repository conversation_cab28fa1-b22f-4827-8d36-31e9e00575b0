/* 智能零售标签系统 - 前端样式文件 */

/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 应用容器 */
.app-container {
    display: flex;
    min-height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

/* 左侧导航栏 */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1000;
}

/* 侧边栏头部 */
.sidebar-header {
    padding: 30px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-img {
    width: 160px;
    height: 49px;
    border-radius: 8px;
    object-fit: contain;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.1);
    padding: 5px;
}

.logo h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ecf0f1;
    line-height: 1.3;
}

/* 导航菜单 */
.nav-menu {
    list-style: none;
    padding: 20px 0;
    flex: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    color: #bdc3c7;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ecf0f1;
    border-left-color: #3498db;
}

.nav-item.active {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
    border-left-color: #3498db;
    font-weight: 600;
}

.nav-item i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.nav-item span {
    font-size: 1rem;
}

/* 连接状态区域 */
.connection-status {
    padding: 20px 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e74c3c;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #27ae60;
}

.status-text {
    font-size: 0.9rem;
    color: #bdc3c7;
}

.connect-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.connect-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    background: #f8f9fa;
    overflow-y: auto;
    position: relative;
}

/* 页面内容 */
.page-content {
    display: none;
    padding: 30px;
    min-height: 100vh;
}

.page-content.active {
    display: block;
}

/* 页面头部 */
.page-header {
    margin-bottom: 40px;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.header-logo {
    width: 300px;
    height: 92px;
    border-radius: 15px;
    object-fit: contain;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.95);
    padding: 10px;
}

.header-text h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
    font-size: 1.4rem;
    opacity: 1;
    font-weight: 400;
    line-height: 1.6;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 8px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 概览卡片 */
.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 50px;
}

.card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.card-icon i {
    font-size: 1.8rem;
    color: white;
}

.card-content h3 {
    font-size: 1.4rem;
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.card-content p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 20px;
}

.card-stats {
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #495057;
    font-size: 0.9rem;
}

.stat-item i {
    color: #667eea;
}

/* 系统特性展示 */
.features-section {
    margin-bottom: 50px;
}

.features-section h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
    font-weight: 600;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.feature-item {
    background: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-item h4 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.feature-item p {
    color: #6c757d;
    line-height: 1.5;
    font-size: 0.95rem;
}

/* 系统架构图 */
.architecture-section {
    margin-bottom: 50px;
}

.architecture-section h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
    font-weight: 600;
}

.architecture-diagram {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.arch-layer {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    position: relative;
}

.arch-layer:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 30px;
    background: linear-gradient(180deg, #3498db 0%, #2980b9 100%);
}

.arch-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    min-width: 200px;
}

.arch-item i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.arch-item span {
    font-size: 1.1rem;
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

.arch-item small {
    font-size: 0.85rem;
    opacity: 0.9;
}

.terminal-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 10px;
}

.terminal-item {
    background: rgba(255, 255, 255, 0.2);
    padding: 10px 15px;
    border-radius: 8px;
    text-align: center;
    min-width: 80px;
}

.terminal-item i {
    font-size: 1.2rem;
    margin-bottom: 5px;
    display: block;
}

.terminal-item span {
    font-size: 0.8rem;
}

/* 终端页面样式 */
.terminal-config {
    background: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.config-group label {
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

.config-group input {
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.config-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.config-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* 状态面板 */
.status-panel {
    background: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.status-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
}

.status-value {
    font-weight: 700;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-connected {
    background: #d4edda;
    color: #155724;
}

.status-disconnected {
    background: #f8d7da;
    color: #721c24;
}

.status-connecting {
    background: #fff3cd;
    color: #856404;
}

/* 消息区域样式 */
.message-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
}

.message-display {
    margin-bottom: 20px;
}

.message-container {
    height: 400px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    font-family: 'Consolas', 'Monaco', monospace;
}

.welcome-message {
    text-align: center;
    color: #6c757d;
    padding: 50px 20px;
}

.welcome-message p {
    margin-bottom: 10px;
}

.message-item {
    margin-bottom: 15px;
    padding: 12px 16px;
    border-radius: 10px;
    word-wrap: break-word;
}

.message-sent {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    margin-left: 20px;
}

.message-received {
    background: #f3e5f5;
    border-left: 4px solid #9c27b0;
    margin-right: 20px;
}

.message-system {
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    font-style: italic;
    text-align: center;
}

.message-timestamp {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 5px;
}

/* 消息输入区域 */
.message-input {
    margin-top: 20px;
}

.input-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

#messageInput {
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

#messageInput:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover:not(:disabled) {
    background: #667eea;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

/* 其他页面样式 */
.page-header h1 {
    font-size: 2.2rem;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-header p {
    color: #6c757d;
    font-size: 1.1rem;
}

.coming-soon {
    text-align: center;
    padding: 80px 20px;
    color: #6c757d;
}

.coming-soon i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #bdc3c7;
}

.coming-soon h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: #495057;
}

.coming-soon p {
    font-size: 1.1rem;
    line-height: 1.6;
}

/* 定位界面样式 */
.map-control-panel {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.control-section h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.control-buttons .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
    min-width: auto;
}

.location-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.location-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.location-stats .stat-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.location-stats .stat-value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1rem;
}

/* 地图容器 */
.map-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
}

.map-display {
    width: 100%;
    height: 500px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
}

.map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #6c757d;
}

.map-loading i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #667eea;
}

.map-loading p {
    font-size: 1.1rem;
    margin: 0;
}

/* 设备列表面板 */
.device-list-panel {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
}

.device-list-panel h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.device-list {
    max-height: 300px;
    overflow-y: auto;
}

.no-devices {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-devices i {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: #bdc3c7;
}

.no-devices p {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.no-devices small {
    font-size: 0.9rem;
    color: #adb5bd;
}

.device-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.device-item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateY(-1px);
}

.device-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.device-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.device-details h4 {
    font-size: 1rem;
    color: #2c3e50;
    margin-bottom: 3px;
    font-weight: 600;
}

.device-details p {
    font-size: 0.85rem;
    color: #6c757d;
    margin: 0;
}

.device-actions {
    display: flex;
    gap: 8px;
}

.device-actions .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    min-width: auto;
}

.device-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.device-status.online {
    background: #d4edda;
    color: #155724;
}

.device-status.offline {
    background: #f8d7da;
    color: #721c24;
}

/* AI调控界面样式 */
.ai-config-panel {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.config-section h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.config-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.config-form .input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-form label {
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

.config-form input,
.config-form select {
    padding: 10px 12px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.config-form input:focus,
.config-form select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.config-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.ai-status {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.ai-status .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.ai-status .status-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.ai-status .status-value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 0.95rem;
}

/* AI对话容器 */
.ai-chat-container {
    background: white;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h3 {
    font-size: 1.3rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-controls {
    display: flex;
    gap: 10px;
}

.chat-controls .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.chat-controls .btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.chat-messages {
    height: 400px;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.welcome-message {
    display: flex;
    gap: 15px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ai-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.message-content h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.message-content p {
    color: #6c757d;
    margin-bottom: 10px;
    line-height: 1.5;
}

.message-content ul {
    color: #6c757d;
    margin: 10px 0;
    padding-left: 20px;
}

.message-content li {
    margin-bottom: 5px;
}

.chat-message {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
}

.chat-message.user {
    flex-direction: row-reverse;
}

.chat-message.user .message-bubble {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.chat-message.assistant .message-bubble {
    background: white;
    border: 1px solid #e9ecef;
}

.message-bubble {
    max-width: 70%;
    padding: 15px 18px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
}

.message-time {
    font-size: 0.75rem;
    color: #adb5bd;
    margin-top: 5px;
    text-align: center;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 15px 18px;
    background: white;
    border-radius: 18px;
    border: 1px solid #e9ecef;
    max-width: 100px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation: typingPulse 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

/* 聊天输入区域 */
.chat-input-area {
    padding: 20px 25px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chat-input-area .input-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 15px;
}

.chat-input-area textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.chat-input-area textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.chat-input-area .input-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.chat-input-area .btn {
    padding: 12px 20px;
    min-width: auto;
    font-size: 0.95rem;
}

/* 智能控制模式切换 */
.control-mode-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toggle-switch {
    position: relative;
    width: 50px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.toggle-switch.active {
    background: #667eea;
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch.active .toggle-slider {
    transform: translateX(26px);
}

.toggle-label {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

.control-mode-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.control-mode-indicator.chat {
    background: #e3f2fd;
    color: #1976d2;
}

.control-mode-indicator.control {
    background: #fff3e0;
    color: #f57c00;
}

/* 控制指令卡片样式 */
.control-command-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    border: none;
}

.control-command-card .card-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.control-command-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.control-command-card .card-icon {
    font-size: 1.3rem;
}

.control-command-card .card-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.control-field {
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 15px;
    border-radius: 8px;
    backdrop-filter: blur(5px);
}

.control-field-label {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-field-value {
    font-size: 1rem;
    font-weight: 600;
    word-break: break-word;
}

.control-reason {
    grid-column: 1 / -1;
    background: rgba(255, 255, 255, 0.15);
    padding: 12px 15px;
    border-radius: 8px;
    border-left: 4px solid rgba(255, 255, 255, 0.5);
}

.control-reason .control-field-label {
    color: rgba(255, 255, 255, 0.9);
}

.control-reason .control-field-value {
    font-style: italic;
    line-height: 1.4;
}

/* 多个控制指令的容器 */
.control-commands-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.control-commands-container .control-command-card:nth-child(even) {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.control-commands-container .control-command-card:nth-child(3n) {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
}

/* 显示调整界面样式 */
.device-status-panel {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.status-section h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.device-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.device-stats .stat-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.device-stats .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.device-stats .stat-label {
    display: block;
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 500;
}

.device-stats .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.batch-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.batch-controls .btn {
    justify-content: flex-start;
    gap: 8px;
}

/* 搜索筛选面板 */
.search-filter-panel {
    background: white;
    border-radius: 12px;
    padding: 20px 25px;
    margin-bottom: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 25px;
    align-items: center;
}

.search-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input-group input {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.search-input-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input-group .btn {
    padding: 10px 16px;
    font-size: 0.9rem;
    min-width: auto;
}

.filter-section {
    display: flex;
    gap: 15px;
}

.filter-section select {
    padding: 8px 12px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 0.9rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.filter-section select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
}

/* 设备列表容器 */
.device-list-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.device-list-header {
    background: #f8f9fa;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.device-list-header h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.list-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.device-count {
    font-size: 0.9rem;
    color: #6c757d;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-controls .btn {
    padding: 6px 12px;
    font-size: 0.85rem;
    min-width: auto;
}

.page-info {
    font-size: 0.85rem;
    color: #6c757d;
    white-space: nowrap;
}

/* 设备网格 */
.device-grid {
    padding: 25px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 20px;
    max-height: 600px;
    overflow-y: auto;
}

.no-devices {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-devices i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #bdc3c7;
}

.no-devices p {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.no-devices small {
    font-size: 0.9rem;
    color: #adb5bd;
}

/* 设备卡片样式 */
.device-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
}

.device-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.device-card.saved {
    border-left: 4px solid #28a745;
    background: #f8fff9;
}

.device-card.pending {
    border-left: 4px solid #ffc107;
    background: #fffef8;
}

.device-card.updated {
    border-left: 4px solid #17a2b8;
    background: #f8feff;
}

.device-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.device-id {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.device-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.device-status.saved {
    background: #d4edda;
    color: #155724;
}

.device-status.pending {
    background: #fff3cd;
    color: #856404;
}

.device-status.updated {
    background: #d1ecf1;
    color: #0c5460;
}

.device-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    width: 100%;
    box-sizing: border-box;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 0; /* 允许flex项目收缩 */
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-size: 0.85rem;
    font-weight: 600;
    color: #495057;
}

.form-group input,
.form-group select {
    width: 100%;
    max-width: 100%;
    padding: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.device-actions {
    margin-top: 15px;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.device-actions .btn {
    padding: 6px 12px;
    font-size: 0.85rem;
    min-width: auto;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.85rem;
    min-width: auto;
}

/* 城市选择器样式 */
.city-select {
    position: relative;
}

.city-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.city-dropdown.show {
    display: block;
}

.city-option {
    padding: 8px 12px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.city-option:hover {
    background: #f8f9fa;
}

.city-option.selected {
    background: #667eea;
    color: white;
}

/* 批量更新确认弹窗 */
.update-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.update-modal.show {
    display: flex;
}

.update-modal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.update-modal h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.update-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
}

.update-summary p {
    margin: 5px 0;
    color: #495057;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* 快捷问题 */
.quick-questions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.quick-btn {
    padding: 6px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    font-size: 0.85rem;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* AI分析面板 */
.ai-analysis-panel {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
}

.ai-analysis-panel h3 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.analysis-content {
    min-height: 200px;
}

.no-analysis {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-analysis i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #bdc3c7;
}

.no-analysis p {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.no-analysis small {
    font-size: 0.9rem;
    color: #adb5bd;
}

.analysis-result {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #667eea;
}

.analysis-result h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.analysis-result .result-content {
    color: #495057;
    line-height: 1.6;
}

.analysis-result .result-tags {
    margin-top: 15px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.result-tag {
    padding: 4px 8px;
    background: #667eea;
    color: white;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typingPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    padding: 25px 30px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.8rem;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    line-height: 1;
}

.modal-close:hover {
    color: #495057;
}

.modal-body {
    padding: 25px 30px;
}

.modal-footer {
    padding: 0 30px 25px;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

.input-group input {
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-hint {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
    line-height: 1.3;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 滚动条样式 */
.message-container::-webkit-scrollbar {
    width: 8px;
}

.message-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.message-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.message-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .sidebar {
        width: 250px;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .header-logo {
        width: 250px;
        height: 76px;
    }

    .header-text h1 {
        font-size: 2.5rem;
    }

    .overview-cards {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        flex-direction: row;
        padding: 0;
    }

    .sidebar-header {
        padding: 15px 20px;
        border-bottom: none;
        border-right: 1px solid rgba(255, 255, 255, 0.1);
    }

    .logo {
        gap: 10px;
    }

    .logo-img {
        width: 130px;
        height: 40px;
    }

    .logo h3 {
        font-size: 1rem;
    }

    .nav-menu {
        display: flex;
        padding: 0;
        overflow-x: auto;
        flex: 1;
    }

    .nav-item {
        flex-direction: column;
        gap: 5px;
        padding: 15px 20px;
        min-width: 80px;
        text-align: center;
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .nav-item.active {
        border-left: none;
        border-bottom-color: #3498db;
    }

    .nav-item i {
        font-size: 1.1rem;
    }

    .nav-item span {
        font-size: 0.8rem;
    }

    .connection-status {
        padding: 15px 20px;
        border-top: none;
        border-left: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: 0;
    }

    .main-content {
        padding: 20px;
    }

    .page-content {
        padding: 20px;
    }

    .header-content {
        padding: 30px 20px;
    }

    .header-text h1 {
        font-size: 2rem;
    }

    .overview-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .arch-layer {
        flex-direction: column;
        align-items: center;
    }

    .terminal-group {
        flex-direction: column;
        gap: 10px;
    }

    .status-panel {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .config-actions {
        justify-content: center;
    }

    .input-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 480px) {
    .sidebar-header {
        padding: 10px 15px;
    }

    .nav-item {
        padding: 10px 15px;
        min-width: 70px;
    }

    .connection-status {
        padding: 10px 15px;
    }

    .main-content {
        padding: 15px;
    }

    .page-content {
        padding: 15px;
    }

    .header-content {
        padding: 20px 15px;
    }

    .header-text h1 {
        font-size: 1.8rem;
    }

    .header-logo {
        width: 200px;
        height: 61px;
    }

    .header-subtitle {
        font-size: 1.1rem;
        padding: 6px 12px;
        font-weight: 500;
    }

    .card {
        padding: 20px;
    }

    .feature-item {
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .arch-item {
        padding: 15px 20px;
        min-width: 150px;
    }

    .message-container {
        height: 300px;
    }

    .page-header h1 {
        font-size: 1.8rem;
    }

    /* 定位界面响应式 */
    .map-control-panel {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }

    .control-buttons {
        justify-content: center;
    }

    .map-display {
        height: 400px;
    }

    .device-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .device-actions {
        width: 100%;
        justify-content: center;
    }

    /* AI调控界面响应式 */
    .ai-config-panel {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }

    .config-actions {
        flex-direction: column;
    }

    .chat-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .chat-controls {
        justify-content: center;
    }

    .chat-input-area .input-container {
        gap: 10px;
    }

    .chat-input-area .input-actions {
        flex-direction: column;
        gap: 15px;
    }

    .control-mode-toggle {
        justify-content: center;
    }

    .control-command-card .card-body {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .control-reason {
        grid-column: 1;
    }

    .quick-questions {
        justify-content: center;
    }

    .message-bubble {
        max-width: 85%;
    }

    /* 显示调整界面响应式 */
    .device-status-panel {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }

    .device-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .search-filter-panel {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px 20px;
    }

    .search-input-group {
        flex-direction: column;
        gap: 10px;
    }

    .filter-section {
        flex-direction: column;
        gap: 10px;
    }

    .device-list-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .list-controls {
        flex-direction: column;
        gap: 10px;
    }

    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .device-grid {
        grid-template-columns: 1fr;
        padding: 15px;
    }

    .device-form {
        grid-template-columns: 1fr;
    }

    .device-actions {
        flex-direction: column;
    }

    .batch-controls {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .update-modal-content {
        padding: 20px;
        margin: 20px;
    }
}

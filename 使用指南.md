# 🚀 智能标签管理系统 - 快速使用指南

## 📋 启动步骤

### Windows用户
1. **双击运行** `start.bat` 文件
2. 等待系统自动启动前后端服务器
3. 在浏览器中访问 `http://localhost:3000`


### 其他系统用户
1. **打开终端**，进入项目目录
2. **运行启动脚本**：`node start.js`
3. 在浏览器中访问 `http://localhost:3000`

## 🌐 访问地址

- **前端界面**: http://localhost:3000
- **WebSocket服务器**: ws://localhost:8080

## 📱 使用方法

### 1. 连接服务器
- 打开前端页面后，默认已填入服务器地址
- 点击"连接服务器"按钮建立连接
- 连接成功后状态会显示为"已连接"

### 2. 发送消息
- 在消息输入框中输入要发送的内容
- 点击"发送消息"按钮或按Enter键发送
- 消息会实时转发给所有其他连接的客户端

### 3. 查看消息
- **发送的消息**：显示为蓝色，标记为"我"
- **接收的消息**：显示为紫色，标记为"其他用户"
- **系统消息**：显示为橙色，标记为"系统"

### 4. 系统信息
- **连接状态**：实时显示连接状态
- **发送/接收计数**：统计消息数量
- **连接时长**：显示连接持续时间
- **最后活动**：显示最后一次活动时间

## ⚙️ 自定义配置

### 修改端口
如果默认端口被占用，可以设置环境变量：

**Windows (命令行)**:
```cmd
set HTTP_PORT=3001
set WS_PORT=8081
node start.js
```

**Linux/Mac**:
```bash
HTTP_PORT=3001 WS_PORT=8081 node start.js
```

### 修改服务器地址
在前端界面中可以直接修改服务器地址和端口，支持：
- localhost (本地)
- 局域网IP地址
- 远程服务器地址

## 🔧 故障排除

### 常见问题

**Q: 页面打不开**
- 确认HTTP服务器已启动（看到"前端HTTP服务器启动成功"）
- 检查浏览器地址是否正确：http://localhost:3000

**Q: 无法连接WebSocket**
- 确认WebSocket服务器已启动（看到"WebSocket服务器启动成功"）
- 检查服务器地址和端口设置
- 确认防火墙没有阻止连接

**Q: 端口被占用**
- 关闭占用端口的其他程序
- 或使用上面的方法修改端口

**Q: 消息发送失败**
- 检查网络连接
- 确认WebSocket连接状态正常
- 查看是否有错误提示

## 📞 获取帮助

如果遇到问题：
1. 查看控制台输出的错误信息
2. 检查浏览器开发者工具的控制台
3. 参考完整的README.md文档
4. 检查系统要求和环境配置

## 🎉 开始使用

现在您可以：
1. 打开多个浏览器标签页测试多客户端通信
2. 在不同设备上访问系统进行测试
3. 根据需要自定义配置和功能

**祝您使用愉快！** 🚀

// 智能零售标签系统 - 前端脚本文件

class SmartRetailSystem {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
        this.reconnectTimer = null;
        this.connectionTimer = null;
        this.connectionStartTime = null;
        this.sentCount = 0;
        this.receivedCount = 0;
        this.connectedAddress = '';
        this.currentPage = 'home';
        this.map = null;
        this.markers = new Map(); // 存储设备标记
        this.deviceList = new Map(); // 存储设备信息
        this.currentDevice = null; // 当前在线设备
        this.deviceTimeout = null; // 设备超时定时器
        this.DEVICE_TIMEOUT = 120000; // 120秒（2分钟）超时

        // AI相关变量
        this.aiConfig = {
            apiKey: '',
            model: 'glm-4-plus',
            baseUrl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
        };
        this.chatHistory = [];
        this.apiCallCount = 0;
        this.isAiTyping = false;
        this.isControlMode = false; // 智能控制模式标志

        // 显示调整相关变量
        this.devices = new Map(); // 存储设备信息
        this.savedDevices = new Set(); // 已保存的设备ID
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.filteredDevices = [];
        this.cities = []; // 中国地级市列表

        this.initElements();
        this.bindEvents();
        this.loadConfig();
        this.initNavigation();
    }
    
    // 初始化DOM元素引用
    initElements() {
        console.log('正在初始化DOM元素...');
        this.elements = {
            // 导航元素
            navItems: document.querySelectorAll('.nav-item'),
            pageContents: document.querySelectorAll('.page-content'),
            
            // 连接相关元素
            connectBtn: document.getElementById('connectBtn'),
            disconnectBtn: document.getElementById('disconnectBtn'),
            connectionDot: document.getElementById('connectionDot'),
            connectionText: document.getElementById('connectionText'),
            connectionModal: document.getElementById('connectionModal'),
            modalClose: document.getElementById('modalClose'),
            modalCancel: document.getElementById('modalCancel'),
            modalConnect: document.getElementById('modalConnect'),
            modalServerAddress: document.getElementById('modalServerAddress'),
            
            // 终端页面元素
            serverAddress: document.getElementById('serverAddress'),
            connectionStatus: document.getElementById('connectionStatus'),
            serverAddressDisplay: document.getElementById('serverAddressDisplay'),
            clientId: document.getElementById('clientId'),
            messageContainer: document.getElementById('messageContainer'),
            messageInput: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            clearBtn: document.getElementById('clearBtn'),
            sentCount: document.getElementById('sentCount'),
            receivedCount: document.getElementById('receivedCount'),
            connectionTime: document.getElementById('connectionTime'),
            lastActivity: document.getElementById('lastActivity'),

            // 定位界面元素
            mapContainer: document.getElementById('mapContainer'),
            centerMapBtn: document.getElementById('centerMapBtn'),
            clearMarkersBtn: document.getElementById('clearMarkersBtn'),
            refreshMapBtn: document.getElementById('refreshMapBtn'),
            onlineDevices: document.getElementById('onlineDevices'),
            totalMarkers: document.getElementById('totalMarkers'),
            lastLocationUpdate: document.getElementById('lastLocationUpdate'),
            deviceList: document.getElementById('deviceList'),

            // AI调控界面元素
            apiKey: document.getElementById('apiKey'),
            aiModel: document.getElementById('aiModel'),
            saveConfigBtn: document.getElementById('saveConfigBtn'),
            testConnectionBtn: document.getElementById('testConnectionBtn'),
            apiStatus: document.getElementById('apiStatus'),
            currentModel: document.getElementById('currentModel'),
            apiCallCount: document.getElementById('apiCallCount'),
            chatMessages: document.getElementById('chatMessages'),
            chatInput: document.getElementById('chatInput'),
            sendChatBtn: document.getElementById('sendChatBtn'),
            clearChatBtn: document.getElementById('clearChatBtn'),
            exportChatBtn: document.getElementById('exportChatBtn'),
            voiceInputBtn: document.getElementById('voiceInputBtn'),
            analysisContent: document.getElementById('analysisContent'),
            controlModeToggle: document.getElementById('controlModeToggle'),
            modeIndicator: document.getElementById('modeIndicator'),

            // 显示调整界面元素
            totalDevices: document.getElementById('totalDevices'),
            savedDevices: document.getElementById('savedDevices'),
            pendingDevices: document.getElementById('pendingDevices'),
            batchUpdateBtn: document.getElementById('batchUpdateBtn'),
            clearAllBtn: document.getElementById('clearAllBtn'),
            exportDataBtn: document.getElementById('exportDataBtn'),
            searchInput: document.getElementById('searchInput'),
            searchBtn: document.getElementById('searchBtn'),
            clearSearchBtn: document.getElementById('clearSearchBtn'),
            statusFilter: document.getElementById('statusFilter'),
            cityFilter: document.getElementById('cityFilter'),
            deviceGrid: document.getElementById('deviceGrid'),
            displayedCount: document.getElementById('displayedCount'),
            currentPageSpan: document.getElementById('currentPage'),
            totalPages: document.getElementById('totalPages'),
            prevPageBtn: document.getElementById('prevPageBtn'),
            nextPageBtn: document.getElementById('nextPageBtn')
        };

        console.log('DOM元素初始化完成:', {
            navItems: this.elements.navItems.length,
            connectBtn: !!this.elements.connectBtn,
            pageContents: this.elements.pageContents.length
        });
    }
    
    // 绑定事件监听器
    bindEvents() {
        // 导航事件
        this.elements.navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const page = e.currentTarget.dataset.page;
                this.switchPage(page);
            });
        });
        
        // 连接按钮事件
        if (this.elements.connectBtn) {
            this.elements.connectBtn.addEventListener('click', () => this.showConnectionModal());
        }
        
        if (this.elements.disconnectBtn) {
            this.elements.disconnectBtn.addEventListener('click', () => this.disconnect());
        }
        
        // 模态框事件
        if (this.elements.modalClose) {
            this.elements.modalClose.addEventListener('click', () => this.hideConnectionModal());
        }
        
        if (this.elements.modalCancel) {
            this.elements.modalCancel.addEventListener('click', () => this.hideConnectionModal());
        }
        
        if (this.elements.modalConnect) {
            this.elements.modalConnect.addEventListener('click', () => this.connectFromModal());
        }
        
        // 消息发送事件
        if (this.elements.sendBtn) {
            this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        }
        
        if (this.elements.messageInput) {
            this.elements.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }
        
        // 清空消息事件
        if (this.elements.clearBtn) {
            this.elements.clearBtn.addEventListener('click', () => this.clearMessages());
        }
        
        // 模态框外部点击关闭
        if (this.elements.connectionModal) {
            this.elements.connectionModal.addEventListener('click', (e) => {
                if (e.target === this.elements.connectionModal) {
                    this.hideConnectionModal();
                }
            });
        }

        // 定位界面事件
        if (this.elements.centerMapBtn) {
            this.elements.centerMapBtn.addEventListener('click', () => this.centerMap());
        }

        if (this.elements.clearMarkersBtn) {
            this.elements.clearMarkersBtn.addEventListener('click', () => this.clearAllMarkers());
        }

        if (this.elements.refreshMapBtn) {
            this.elements.refreshMapBtn.addEventListener('click', () => this.refreshMap());
        }

        // AI调控界面事件
        if (this.elements.saveConfigBtn) {
            this.elements.saveConfigBtn.addEventListener('click', () => this.saveAiConfig());
        }

        if (this.elements.testConnectionBtn) {
            this.elements.testConnectionBtn.addEventListener('click', () => this.testAiConnection());
        }

        if (this.elements.sendChatBtn) {
            this.elements.sendChatBtn.addEventListener('click', () => this.sendChatMessage());
        }

        if (this.elements.clearChatBtn) {
            this.elements.clearChatBtn.addEventListener('click', () => this.clearChatHistory());
        }

        if (this.elements.exportChatBtn) {
            this.elements.exportChatBtn.addEventListener('click', () => this.exportChatHistory());
        }

        if (this.elements.controlModeToggle) {
            this.elements.controlModeToggle.addEventListener('click', () => this.toggleControlMode());
        }

        // 显示调整界面事件
        if (this.elements.batchUpdateBtn) {
            this.elements.batchUpdateBtn.addEventListener('click', () => this.batchUpdateDevices());
        }

        if (this.elements.clearAllBtn) {
            this.elements.clearAllBtn.addEventListener('click', () => this.clearAllDevices());
        }

        if (this.elements.exportDataBtn) {
            this.elements.exportDataBtn.addEventListener('click', () => this.exportDeviceData());
        }

        if (this.elements.searchBtn) {
            this.elements.searchBtn.addEventListener('click', () => this.searchDevices());
        }

        if (this.elements.clearSearchBtn) {
            this.elements.clearSearchBtn.addEventListener('click', () => this.clearSearch());
        }

        if (this.elements.searchInput) {
            this.elements.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchDevices();
                }
            });
        }

        if (this.elements.statusFilter) {
            this.elements.statusFilter.addEventListener('change', () => this.filterDevices());
        }

        if (this.elements.cityFilter) {
            this.elements.cityFilter.addEventListener('change', () => this.filterDevices());
        }

        if (this.elements.prevPageBtn) {
            this.elements.prevPageBtn.addEventListener('click', () => this.previousPage());
        }

        if (this.elements.nextPageBtn) {
            this.elements.nextPageBtn.addEventListener('click', () => this.nextPage());
        }

        // Shell命令快捷按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.shell-cmd-btn')) {
                const btn = e.target.closest('.shell-cmd-btn');
                const command = btn.dataset.command;
                if (command) {
                    this.executeShellCommand(command);
                }
            }
        });

        if (this.elements.chatInput) {
            this.elements.chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendChatMessage();
                }
            });
        }

        // 快捷问题按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-btn')) {
                const question = e.target.dataset.question;
                if (question && this.elements.chatInput) {
                    this.elements.chatInput.value = question;
                    this.sendChatMessage();
                }
            }
        });
    }
    
    // 初始化导航
    initNavigation() {
        this.switchPage('home');
    }
    
    // 切换页面
    switchPage(page) {
        // 更新导航状态
        this.elements.navItems.forEach(item => {
            item.classList.remove('active');
            if (item.dataset.page === page) {
                item.classList.add('active');
            }
        });
        
        // 更新页面内容
        this.elements.pageContents.forEach(content => {
            content.classList.remove('active');
            if (content.id === `${page}-page`) {
                content.classList.add('active');
            }
        });
        
        this.currentPage = page;

        // 如果切换到定位页面，初始化地图
        if (page === 'location') {
            // 强制重新初始化地图，解决显示问题
            setTimeout(() => {
                if (this.map) {
                    try {
                        this.map.remove();
                        this.map = null;
                    } catch (e) {
                        console.warn('移除旧地图实例时出错:', e);
                    }
                }
                this.initMap();
            }, 200);
        }

        // 如果切换到AI页面，初始化AI配置
        if (page === 'ai') {
            setTimeout(() => this.initAiInterface(), 100);
        }

        // 如果切换到显示调整页面，初始化界面
        if (page === 'display') {
            setTimeout(() => this.initDisplayInterface(), 100);
        }
    }
    
    // 显示连接模态框
    showConnectionModal() {
        if (this.elements.connectionModal) {
            this.elements.connectionModal.classList.add('active');
            if (this.elements.modalServerAddress) {
                this.elements.modalServerAddress.focus();
            }
        }
    }
    
    // 隐藏连接模态框
    hideConnectionModal() {
        if (this.elements.connectionModal) {
            this.elements.connectionModal.classList.remove('active');
        }
    }
    
    // 从模态框连接
    connectFromModal() {
        const address = this.elements.modalServerAddress?.value.trim();
        if (address) {
            this.connect(address);
            this.hideConnectionModal();
        }
    }
    
    // 连接WebSocket服务器
    connect(address = null) {
        if (this.isConnected) return;
        
        const serverAddress = address || this.elements.serverAddress?.value.trim() || 'localhost:8082';
        
        // 构建WebSocket URL
        let url;
        if (serverAddress.startsWith('ws://') || serverAddress.startsWith('wss://')) {
            url = serverAddress;
        } else {
            url = `ws://${serverAddress}`;
        }
        
        this.connectedAddress = serverAddress;
        this.updateConnectionStatus('connecting', '连接中...');
        this.addSystemMessage(`正在连接到 ${url}...`);
        
        try {
            this.ws = new WebSocket(url);
            this.setupWebSocketEvents();
        } catch (error) {
            this.handleConnectionError('连接失败: ' + error.message);
        }
    }
    
    // 设置WebSocket事件监听
    setupWebSocketEvents() {
        this.ws.onopen = () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.connectionStartTime = new Date();
            this.startConnectionTimer();
            
            console.log('WebSocket连接成功:', this.connectedAddress);
            console.log('WebSocket URL:', this.ws.url);
            this.updateConnectionStatus('connected', '已连接');
            if (this.elements.serverAddressDisplay) {
                this.elements.serverAddressDisplay.textContent = this.connectedAddress;
            }
            this.addSystemMessage(`✅ 成功连接到服务器: ${this.connectedAddress}`);
            this.addSystemMessage(`🔗 实际连接URL: ${this.ws.url}`);
            this.updateUI();
        };
        
        this.ws.onmessage = (event) => {
            console.log('收到WebSocket消息:', event.data);
            this.handleMessage(event.data);
            this.updateLastActivity();
        };
        
        this.ws.onclose = (event) => {
            this.isConnected = false;
            this.clearConnectionTimer();
            this.updateConnectionStatus('disconnected', '连接已断开');
            this.addSystemMessage('❌ 连接已断开');
            this.updateUI();
            
            if (this.reconnectAttempts < this.maxReconnectAttempts && !event.wasClean) {
                this.scheduleReconnect();
            }
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.addSystemMessage('❌ 连接错误');
        };
    }
    
    // 断开连接
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, '用户主动断开');
            this.clearReconnectTimer();
        }
    }
    
    // 处理接收到的消息
    handleMessage(data) {
        this.receivedCount++;
        if (this.elements.receivedCount) {
            this.elements.receivedCount.textContent = this.receivedCount;
        }

        // 检查是否为位置信息格式：经度_纬度
        if (this.isLocationData(data)) {
            this.handleLocationMessage(data);
            return;
        }

        // 检查是否为设备数量信息格式：device_number:数量
        if (this.isDeviceNumberData(data)) {
            this.handleDeviceNumberMessage(data);
            return;
        }

        try {
            const message = JSON.parse(data);
            if (message.type === 'welcome') {
                this.clientId = message.clientId;
                if (this.elements.clientId) {
                    this.elements.clientId.textContent = this.clientId;
                }
                this.addSystemMessage(`🆔 客户端ID: ${this.clientId}`);
            } else {
                this.addReceivedMessage(data);
            }
        } catch (error) {
            this.addReceivedMessage(data);
        }
    }
    
    // 发送消息
    sendMessage() {
        if (!this.isConnected || !this.ws) {
            this.addSystemMessage('❌ 请先连接到服务器');
            return;
        }

        const message = this.elements.messageInput?.value.trim();
        if (!message) return;

        try {
            // 检测是否为Shell命令格式并添加适当的行结束符
            let formattedMessage = this.formatShellCommand(message);

            console.log('发送Shell命令:', formattedMessage, '到服务器:', this.connectedAddress);
            console.log('WebSocket状态:', this.ws.readyState);
            console.log('原始命令:', message);
            console.log('格式化后:', JSON.stringify(formattedMessage));

            this.ws.send(formattedMessage);
            this.sentCount++;
            if (this.elements.sentCount) {
                this.elements.sentCount.textContent = this.sentCount;
            }
            this.addSentMessage(message); // 显示原始命令
            if (this.elements.messageInput) {
                this.elements.messageInput.value = '';
            }
            this.updateLastActivity();
            console.log('Shell命令发送成功');
        } catch (error) {
            console.error('发送Shell命令失败:', error);
            this.addSystemMessage('❌ 发送Shell命令失败: ' + error.message);
        }
    }

    // 格式化Shell命令
    formatShellCommand(command) {
        // 您的Shell支持的命令列表
        const shellCommands = [
            'help',        // 显示帮助信息
            'ls',          // 列出目录内容
            'cd',          // 改变工作目录
            'pwd',         // 打印当前目录
            'cat',         // 显示文件内容
            'mkdir',       // 创建目录
            'rm',          // 删除文件或目录
            'touch',       // 创建文件或更新时间戳
            'mv',          // 移动或重命名
            'cp',          // 复制文件
            'echo',        // 输出文本
            'clear',       // 清除屏幕
            'write',       // 写入文件
            'device_number' // 自定义设备数量命令
        ];

        const commandParts = command.split(' ');
        const baseCommand = commandParts[0].toLowerCase();

        // 如果是Shell命令，确保使用正确的行结束符
        if (shellCommands.includes(baseCommand) || command.includes(':')) {
            // 尝试不同的行结束符格式
            // 根据您的测试，我们使用最标准的格式
            return command + '\r\n';
        }

        // 对于非Shell命令（如设备数量信息），保持原格式
        return command;
    }

    // 执行Shell命令快捷按钮
    executeShellCommand(command) {
        if (!this.isConnected || !this.ws) {
            this.addSystemMessage('❌ 请先连接到服务器');
            return;
        }

        let finalCommand = command;

        // 为需要参数的命令添加交互式输入
        switch (command) {
            case 'cat':
                const filename = prompt('请输入要查看的文件名:', 'example.txt');
                if (filename) {
                    finalCommand = `cat ${filename}`;
                } else {
                    return; // 用户取消
                }
                break;

            case 'cd':
                const directory = prompt('请输入要切换到的目录:', '/');
                if (directory !== null) {
                    finalCommand = directory.trim() ? `cd ${directory}` : 'cd';
                } else {
                    return; // 用户取消
                }
                break;

            case 'mkdir':
                const dirName = prompt('请输入要创建的目录名:', 'newdir');
                if (dirName) {
                    finalCommand = `mkdir ${dirName}`;
                } else {
                    return; // 用户取消
                }
                break;

            case 'touch':
                const fileName = prompt('请输入要创建的文件名:', 'newfile.txt');
                if (fileName) {
                    finalCommand = `touch ${fileName}`;
                } else {
                    return; // 用户取消
                }
                break;

            case 'rm':
                const targetName = prompt('请输入要删除的文件或目录名:', 'filename.txt');
                if (targetName) {
                    const confirmDelete = confirm(`确定要删除 "${targetName}" 吗？此操作不可撤销！`);
                    if (confirmDelete) {
                        finalCommand = `rm ${targetName}`;
                    } else {
                        return; // 用户取消
                    }
                } else {
                    return; // 用户取消
                }
                break;

            case 'echo':
                const text = prompt('请输入要输出的文本:', 'Hello World');
                if (text !== null) {
                    finalCommand = text.trim() ? `echo ${text}` : 'echo';
                } else {
                    return; // 用户取消
                }
                break;

            case 'write':
                const writeFile = prompt('请输入文件名:', 'output.txt');
                if (writeFile) {
                    const writeContent = prompt('请输入要写入的内容:', 'Hello World');
                    if (writeContent !== null) {
                        finalCommand = `write ${writeFile} ${writeContent}`;
                    } else {
                        return; // 用户取消
                    }
                } else {
                    return; // 用户取消
                }
                break;
        }

        // 将命令填入输入框并发送
        if (this.elements.messageInput) {
            this.elements.messageInput.value = finalCommand;
        }

        // 直接发送命令
        this.sendMessage();

        // 添加快捷命令提示
        this.addSystemMessage(`🚀 执行Shell命令: ${finalCommand}`);
    }
    
    // 更新连接状态
    updateConnectionStatus(status, text) {
        if (this.elements.connectionDot) {
            this.elements.connectionDot.className = 'status-dot';
            if (status === 'connected') {
                this.elements.connectionDot.classList.add('connected');
            }
        }
        
        if (this.elements.connectionText) {
            this.elements.connectionText.textContent = text;
        }
        
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.className = `status-value status-${status}`;
            this.elements.connectionStatus.textContent = text;
        }
    }
    
    // 更新界面状态
    updateUI() {
        if (this.elements.connectBtn) {
            this.elements.connectBtn.textContent = this.isConnected ? '已连接' : '连接系统';
            this.elements.connectBtn.disabled = this.isConnected;
        }

        if (this.elements.disconnectBtn) {
            this.elements.disconnectBtn.disabled = !this.isConnected;
        }

        if (this.elements.messageInput) {
            this.elements.messageInput.disabled = !this.isConnected;
        }

        if (this.elements.sendBtn) {
            this.elements.sendBtn.disabled = !this.isConnected;
        }

        if (!this.isConnected) {
            if (this.elements.serverAddressDisplay) {
                this.elements.serverAddressDisplay.textContent = '-';
            }
            if (this.elements.clientId) {
                this.elements.clientId.textContent = '-';
            }
        }
    }

    // 添加系统消息
    addSystemMessage(message) {
        this.addMessage(message, 'system');
    }

    // 添加发送的消息
    addSentMessage(message) {
        this.addMessage(`我: ${message}`, 'sent');
    }

    // 添加接收的消息
    addReceivedMessage(message) {
        this.addMessage(`收到: ${message}`, 'received');
    }

    // 添加消息到容器
    addMessage(content, type) {
        if (!this.elements.messageContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message-item message-${type}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        // 处理所有接收消息的换行符
        if (type === 'received') {
            contentDiv.innerHTML = this.formatReceivedMessage(content);
        } else {
            contentDiv.textContent = content;
        }

        messageDiv.appendChild(contentDiv);

        const timestampDiv = document.createElement('div');
        timestampDiv.className = 'message-timestamp';
        timestampDiv.textContent = new Date().toLocaleTimeString();
        messageDiv.appendChild(timestampDiv);

        this.elements.messageContainer.appendChild(messageDiv);
        this.elements.messageContainer.scrollTop = this.elements.messageContainer.scrollHeight;
    }

    // 格式化接收到的消息
    formatReceivedMessage(content) {
        // 转义HTML特殊字符
        const escapeHtml = (text) => {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        };

        // 处理换行符
        let formatted = escapeHtml(content);

        // 将各种换行符转换为HTML换行
        formatted = formatted
            .replace(/\r\n/g, '<br>')
            .replace(/\n/g, '<br>')
            .replace(/\r/g, '<br>');

        // 检测是否为Shell输出并添加特殊样式
        const isShellOutput = formatted.includes('Directory') ||
                             formatted.includes('[FILE]') ||
                             formatted.includes('[DIR]') ||
                             formatted.includes('Type') ||
                             formatted.includes('Size') ||
                             formatted.includes('Name') ||
                             formatted.includes('----');

        if (isShellOutput) {
            formatted = `<div class="shell-output">${formatted}</div>`;
        }

        return formatted;
    }

    // 清空消息
    clearMessages() {
        if (this.elements.messageContainer) {
            this.elements.messageContainer.innerHTML = `
                <div class="welcome-message">
                    <p>👋 欢迎使用智能标签管理系统终端！</p>
                    <p>请先连接到WebSocket服务器开始使用。</p>
                </div>
            `;
        }
    }

    // 开始连接时长计时
    startConnectionTimer() {
        this.connectionTimer = setInterval(() => {
            if (this.connectionStartTime && this.elements.connectionTime) {
                const duration = Date.now() - this.connectionStartTime.getTime();
                this.elements.connectionTime.textContent = this.formatDuration(duration);
            }
        }, 1000);
    }

    // 清除连接计时器
    clearConnectionTimer() {
        if (this.connectionTimer) {
            clearInterval(this.connectionTimer);
            this.connectionTimer = null;
        }
    }

    // 更新最后活动时间
    updateLastActivity() {
        if (this.elements.lastActivity) {
            this.elements.lastActivity.textContent = new Date().toLocaleTimeString();
        }
    }

    // 格式化持续时间
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        const h = hours.toString().padStart(2, '0');
        const m = (minutes % 60).toString().padStart(2, '0');
        const s = (seconds % 60).toString().padStart(2, '0');

        return `${h}:${m}:${s}`;
    }

    // 处理连接错误
    handleConnectionError(message) {
        this.updateConnectionStatus('disconnected', '连接失败');
        this.addSystemMessage(`❌ ${message}`);
        this.updateUI();
    }

    // 安排重连
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectInterval * this.reconnectAttempts;

        this.addSystemMessage(`🔄 ${delay/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

        this.reconnectTimer = setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }

    // 清除重连定时器
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        this.reconnectAttempts = 0;
    }

    // 加载配置
    loadConfig() {
        try {
            const savedAddress = localStorage.getItem('smartRetail_serverAddress');
            if (savedAddress) {
                if (this.elements.serverAddress) {
                    this.elements.serverAddress.value = savedAddress;
                }
                if (this.elements.modalServerAddress) {
                    this.elements.modalServerAddress.value = savedAddress;
                }
            }
        } catch (error) {
            console.warn('加载配置失败:', error);
        }
    }

    // 保存配置
    saveConfig() {
        try {
            const address = this.elements.serverAddress?.value || this.elements.modalServerAddress?.value;
            if (address) {
                localStorage.setItem('smartRetail_serverAddress', address);
            }
        } catch (error) {
            console.warn('保存配置失败:', error);
        }
    }

    // 检查是否为位置数据格式
    isLocationData(data) {
        const locationPattern = /^(\d+\.\d+)_(\d+\.\d+)$/;
        return locationPattern.test(data.trim());
    }

    // 处理位置消息
    handleLocationMessage(data) {
        const [longitude, latitude] = data.trim().split('_').map(Number);

        if (this.isValidCoordinate(longitude, latitude)) {
            // 中枢设备ID固定为 "central_device"
            const deviceId = "central_device";
            this.updateDeviceLocation(deviceId, longitude, latitude);
            this.addSystemMessage(`📍 收到中枢设备位置: ${longitude}, ${latitude}`);
        } else {
            this.addSystemMessage(`❌ 无效的位置信息: ${data}`);
        }
    }

    // 验证坐标有效性
    isValidCoordinate(lng, lat) {
        // 扩大坐标范围以包含更多区域：经度70-140，纬度15-60
        return lng >= 70 && lng <= 140 && lat >= 15 && lat <= 60;
    }

    // 初始化地图
    initMap() {
        if (!window.L) {
            this.showMapError('地图API加载失败，请检查网络连接');
            return;
        }

        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) {
            this.showMapError('地图容器未找到');
            return;
        }

        try {
            // 创建地图实例，优化性能设置
            this.map = L.map('mapContainer', {
                preferCanvas: true, // 使用Canvas渲染提高性能
                zoomControl: true,
                attributionControl: true
            }).setView([35.0, 105.0], 4); // 中国中心位置

            // 添加瓦片图层 - 使用更快的瓦片服务
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18,
                minZoom: 3,
                updateWhenIdle: true, // 优化性能
                keepBuffer: 2 // 减少缓存
            }).addTo(this.map);

            // 添加比例尺控件
            L.control.scale({
                position: 'bottomright',
                imperial: false
            }).addTo(this.map);

            // 隐藏加载提示
            const loadingEl = document.querySelector('#mapContainer .map-loading');
            if (loadingEl) {
                loadingEl.style.display = 'none';
            }

            console.log('地图初始化成功');
        } catch (error) {
            console.error('地图初始化失败:', error);
            this.showMapError('地图初始化失败: ' + error.message);
        }
    }

    // 显示地图错误
    showMapError(message) {
        if (this.elements.mapContainer) {
            this.elements.mapContainer.innerHTML = `
                <div class="map-loading">
                    <i class="fas fa-exclamation-triangle" style="color: #e74c3c;"></i>
                    <p style="color: #e74c3c;">${message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }

    // 更新设备位置
    updateDeviceLocation(deviceId, longitude, latitude) {
        if (!this.map) {
            console.warn('地图未初始化，无法添加标记');
            return;
        }

        const timestamp = new Date();

        // 创建历史标记（灰色小点）
        const historyIcon = L.divIcon({
            html: `
                <div style="
                    width: 8px;
                    height: 8px;
                    background: #95a5a6;
                    border: 2px solid white;
                    border-radius: 50%;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                "></div>
            `,
            className: 'history-marker',
            iconSize: [12, 12],
            iconAnchor: [6, 6]
        });

        // 添加历史标记
        const historyMarkerId = `history_${timestamp.getTime()}`;
        const historyMarker = L.marker([latitude, longitude], { icon: historyIcon });
        historyMarker.bindPopup(`
            <div style="padding: 5px; min-width: 150px;">
                <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 12px;">📍 历史位置</h4>
                <p style="margin: 2px 0; color: #6c757d; font-size: 11px;"><strong>坐标:</strong> ${longitude.toFixed(6)}, ${latitude.toFixed(6)}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 11px;"><strong>时间:</strong> ${timestamp.toLocaleString()}</p>
            </div>
        `);
        historyMarker.addTo(this.map);
        this.markers.set(historyMarkerId, historyMarker);

        // 更新或创建当前设备标记
        this.updateCurrentDeviceMarker(deviceId, longitude, latitude, timestamp);

        // 重置设备超时定时器
        this.resetDeviceTimeout(deviceId);

        // 更新统计信息
        this.updateLocationStats();

        // 更新设备列表显示
        this.updateDeviceListDisplay();

        // 自动调整地图视野以包含所有标记
        if (this.markers.size > 0) {
            this.fitMapToMarkers();
        }
    }

    // 更新当前设备标记
    updateCurrentDeviceMarker(deviceId, longitude, latitude, timestamp) {
        // 移除旧的当前设备标记
        if (this.currentDevice && this.markers.has(this.currentDevice.markerId)) {
            const oldMarker = this.markers.get(this.currentDevice.markerId);
            this.map.removeLayer(oldMarker);
            this.markers.delete(this.currentDevice.markerId);
        }

        // 创建新的当前设备标记（绿色大点）
        const currentIcon = L.divIcon({
            html: `
                <div style="
                    width: 20px;
                    height: 20px;
                    background: #27ae60;
                    border: 3px solid white;
                    border-radius: 50%;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.4);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                    animation: pulse 2s infinite;
                ">🏢</div>
            `,
            className: 'current-device-marker',
            iconSize: [26, 26],
            iconAnchor: [13, 13]
        });

        const currentMarkerId = `current_${deviceId}`;
        const currentMarker = L.marker([latitude, longitude], { icon: currentIcon });
        currentMarker.bindPopup(`
            <div style="padding: 8px; min-width: 200px;">
                <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">🏢 中枢设备</h4>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>设备ID:</strong> ${deviceId}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>经度:</strong> ${longitude.toFixed(6)}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>纬度:</strong> ${latitude.toFixed(6)}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>状态:</strong> <span style="color: #27ae60;">在线</span></p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>最后更新:</strong> ${timestamp.toLocaleString()}</p>
            </div>
        `);
        currentMarker.addTo(this.map);
        this.markers.set(currentMarkerId, currentMarker);

        // 更新当前设备信息
        this.currentDevice = {
            id: deviceId,
            markerId: currentMarkerId,
            longitude,
            latitude,
            timestamp,
            status: 'online'
        };

        // 更新设备列表中的信息
        this.deviceList.set(deviceId, this.currentDevice);
    }

    // 重置设备超时定时器
    resetDeviceTimeout(deviceId) {
        // 清除旧的定时器
        if (this.deviceTimeout) {
            clearTimeout(this.deviceTimeout);
        }

        // 设置新的定时器
        this.deviceTimeout = setTimeout(() => {
            this.setDeviceOffline(deviceId);
        }, this.DEVICE_TIMEOUT);
    }

    // 设置设备离线
    setDeviceOffline(deviceId) {
        if (this.currentDevice && this.currentDevice.id === deviceId) {
            // 更新设备状态为离线
            this.currentDevice.status = 'offline';
            this.deviceList.set(deviceId, this.currentDevice);

            // 更新当前设备标记为离线状态（红色）
            if (this.markers.has(this.currentDevice.markerId)) {
                const marker = this.markers.get(this.currentDevice.markerId);
                this.map.removeLayer(marker);

                // 创建离线设备标记
                const offlineIcon = L.divIcon({
                    html: `
                        <div style="
                            width: 20px;
                            height: 20px;
                            background: #e74c3c;
                            border: 3px solid white;
                            border-radius: 50%;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.4);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 10px;
                            font-weight: bold;
                        ">❌</div>
                    `,
                    className: 'offline-device-marker',
                    iconSize: [26, 26],
                    iconAnchor: [13, 13]
                });

                const offlineMarker = L.marker([this.currentDevice.latitude, this.currentDevice.longitude], { icon: offlineIcon });
                offlineMarker.bindPopup(`
                    <div style="padding: 8px; min-width: 200px;">
                        <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">🏢 中枢设备</h4>
                        <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>设备ID:</strong> ${deviceId}</p>
                        <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>状态:</strong> <span style="color: #e74c3c;">离线</span></p>
                        <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>最后在线:</strong> ${this.currentDevice.timestamp.toLocaleString()}</p>
                    </div>
                `);
                offlineMarker.addTo(this.map);
                this.markers.set(this.currentDevice.markerId, offlineMarker);
            }

            this.addSystemMessage(`⚠️ 中枢设备已离线（超过2分钟未收到位置信息）`);
            this.updateLocationStats();
            this.updateDeviceListDisplay();
        }
    }

    // 更新位置统计信息
    updateLocationStats() {
        // 在线设备数：只有当前设备在线时为1，否则为0
        const onlineCount = this.currentDevice && this.currentDevice.status === 'online' ? 1 : 0;

        if (this.elements.onlineDevices) {
            this.elements.onlineDevices.textContent = onlineCount;
        }

        if (this.elements.totalMarkers) {
            this.elements.totalMarkers.textContent = this.markers.size;
        }

        if (this.elements.lastLocationUpdate) {
            this.elements.lastLocationUpdate.textContent = new Date().toLocaleTimeString();
        }
    }

    // 更新设备列表显示
    updateDeviceListDisplay() {
        if (!this.elements.deviceList) return;

        if (!this.currentDevice) {
            this.elements.deviceList.innerHTML = `
                <div class="no-devices">
                    <i class="fas fa-microchip"></i>
                    <p>暂无中枢设备信息</p>
                    <small>等待接收位置数据...</small>
                </div>
            `;
            return;
        }

        const device = this.currentDevice;
        const statusText = device.status === 'online' ? '在线' : '离线';
        const statusColor = device.status === 'online' ? '#27ae60' : '#e74c3c';
        const lastUpdate = device.status === 'online' ?
            `最后更新: ${device.timestamp.toLocaleTimeString()}` :
            `最后在线: ${device.timestamp.toLocaleTimeString()}`;

        this.elements.deviceList.innerHTML = `
            <div class="device-item" data-device-id="${device.id}">
                <div class="device-info">
                    <div class="device-icon" style="background: ${statusColor};">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="device-details">
                        <h4>🏢 中枢设备</h4>
                        <p>位置: ${device.longitude.toFixed(6)}, ${device.latitude.toFixed(6)}</p>
                        <p>${lastUpdate}</p>
                    </div>
                </div>
                <div class="device-actions">
                    <span class="device-status ${device.status}">${statusText}</span>
                    <button class="btn btn-outline" onclick="smartRetailSystem.locateDevice('${device.id}')">
                        <i class="fas fa-crosshairs"></i> 定位
                    </button>
                    <button class="btn btn-outline" onclick="smartRetailSystem.clearDeviceHistory()">
                        <i class="fas fa-history"></i> 清除轨迹
                    </button>
                </div>
            </div>
        `;
    }

    // 定位到指定设备
    locateDevice(deviceId) {
        if (this.currentDevice && this.currentDevice.id === deviceId) {
            const marker = this.markers.get(this.currentDevice.markerId);
            if (marker && this.map) {
                const latLng = marker.getLatLng();
                this.map.setView(latLng, 15);

                // 打开弹出窗口
                marker.openPopup();

                this.addSystemMessage(`🎯 已定位到中枢设备`);
            }
        }
    }

    // 清除设备历史轨迹
    clearDeviceHistory() {
        if (!this.map) return;

        // 保留当前设备标记，移除所有历史标记
        const currentMarkerId = this.currentDevice ? this.currentDevice.markerId : null;

        this.markers.forEach((marker, markerId) => {
            if (markerId !== currentMarkerId) {
                this.map.removeLayer(marker);
            }
        });

        // 清除历史标记记录
        const newMarkers = new Map();
        if (currentMarkerId && this.markers.has(currentMarkerId)) {
            newMarkers.set(currentMarkerId, this.markers.get(currentMarkerId));
        }
        this.markers = newMarkers;

        this.updateLocationStats();
        this.addSystemMessage('🧹 已清除历史轨迹，保留当前设备位置');
    }

    // 调整地图视野以包含所有标记
    fitMapToMarkers() {
        if (!this.map || this.markers.size === 0) return;

        const group = new L.featureGroup(Array.from(this.markers.values()));
        this.map.fitBounds(group.getBounds().pad(0.1));
    }

    // 居中显示地图
    centerMap() {
        if (!this.map) return;

        if (this.currentDevice) {
            // 如果有当前设备，直接定位到设备位置
            this.map.setView([this.currentDevice.latitude, this.currentDevice.longitude], 10);
        } else if (this.markers.size > 0) {
            this.fitMapToMarkers();
        } else {
            this.map.setView([35.0, 105.0], 4); // 中国中心
        }
    }

    // 清除所有标记
    clearAllMarkers() {
        if (!this.map) return;

        // 清除设备超时定时器
        if (this.deviceTimeout) {
            clearTimeout(this.deviceTimeout);
            this.deviceTimeout = null;
        }

        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });

        this.markers.clear();
        this.deviceList.clear();
        this.currentDevice = null;

        this.updateLocationStats();
        this.updateDeviceListDisplay();

        this.addSystemMessage('🧹 已清除所有标记和设备信息');
    }

    // 刷新地图
    refreshMap() {
        if (this.map) {
            this.map.remove();
            this.map = null;
        }

        // 清除所有标记和设备信息
        this.markers.clear();
        this.deviceList.clear();
        this.currentDevice = null;

        // 清除设备超时定时器
        if (this.deviceTimeout) {
            clearTimeout(this.deviceTimeout);
            this.deviceTimeout = null;
        }

        setTimeout(() => {
            this.initMap();
            this.updateLocationStats();
            this.updateDeviceListDisplay();
            this.addSystemMessage('🔄 地图已刷新');
        }, 100);
    }

    // ==================== AI调控功能 ====================

    // 初始化AI界面
    initAiInterface() {
        this.loadAiConfig();
        this.updateAiStatus();
    }

    // 加载AI配置
    loadAiConfig() {
        try {
            const savedConfig = localStorage.getItem('smartRetail_aiConfig');
            if (savedConfig) {
                this.aiConfig = { ...this.aiConfig, ...JSON.parse(savedConfig) };

                if (this.elements.apiKey) {
                    this.elements.apiKey.value = this.aiConfig.apiKey;
                }
                if (this.elements.aiModel) {
                    this.elements.aiModel.value = this.aiConfig.model;
                }
            }

            const savedCallCount = localStorage.getItem('smartRetail_apiCallCount');
            if (savedCallCount) {
                this.apiCallCount = parseInt(savedCallCount) || 0;
            }
        } catch (error) {
            console.warn('加载AI配置失败:', error);
        }
    }

    // 保存AI配置
    saveAiConfig() {
        if (!this.elements.apiKey || !this.elements.aiModel) return;

        const apiKey = this.elements.apiKey.value.trim();
        const model = this.elements.aiModel.value;

        if (!apiKey) {
            this.showAiMessage('请输入API密钥', 'error');
            return;
        }

        this.aiConfig.apiKey = apiKey;
        this.aiConfig.model = model;

        try {
            localStorage.setItem('smartRetail_aiConfig', JSON.stringify(this.aiConfig));
            this.updateAiStatus();
            this.updateChatInputState();
            this.showAiMessage('AI配置已保存', 'success');
        } catch (error) {
            console.error('保存AI配置失败:', error);
            this.showAiMessage('保存配置失败: ' + error.message, 'error');
        }
    }

    // 测试AI连接
    async testAiConnection() {
        if (!this.aiConfig.apiKey) {
            this.showAiMessage('请先配置API密钥', 'error');
            return;
        }

        const originalText = this.elements.testConnectionBtn?.textContent;
        if (this.elements.testConnectionBtn) {
            this.elements.testConnectionBtn.innerHTML = '<span class="loading-spinner"></span> 测试中...';
            this.elements.testConnectionBtn.disabled = true;
        }

        try {
            const response = await this.callGLMApi([
                { role: 'user', content: '你好，请简单回复确认连接正常。' }
            ]);

            if (response && response.choices && response.choices[0]) {
                this.showAiMessage('API连接测试成功！', 'success');
                this.updateAiStatus();
            } else {
                throw new Error('API响应格式异常');
            }
        } catch (error) {
            console.error('API连接测试失败:', error);
            this.showAiMessage('API连接测试失败: ' + error.message, 'error');
        } finally {
            if (this.elements.testConnectionBtn) {
                this.elements.testConnectionBtn.textContent = originalText;
                this.elements.testConnectionBtn.disabled = false;
            }
        }
    }

    // 发送聊天消息
    async sendChatMessage() {
        if (!this.elements.chatInput || this.isAiTyping) return;

        const message = this.elements.chatInput.value.trim();
        if (!message) return;

        if (!this.aiConfig.apiKey) {
            this.showAiMessage('请先配置API密钥', 'error');
            return;
        }

        // 清空输入框
        this.elements.chatInput.value = '';

        // 添加用户消息到聊天记录
        this.addChatMessage('user', message);

        // 显示AI正在输入
        this.showAiTyping();

        try {
            // 构建对话历史
            const systemPrompt = this.isControlMode ?
                `你是智能零售标签系统的控制助手。当用户提出设备控制或价格调整需求时，请严格按照以下JSON格式返回控制指令：

{
  "价签终端": {
    "商品名称": "商品名称",
    "显示类型": "价格|促销信息|库存状态",
    "内容": "具体显示文本",
    "理由": "简要说明控制决策"
  }
}

判断规则：
- 商品名称字段必须填写，根据用户提到的商品或从上下文推断
- 如果用户要求价格调整，显示类型为"价格"
- 如果用户要求促销活动，显示类型为"促销信息"
- 如果用户询问库存，显示类型为"库存状态"
- 内容字段填写具体要显示的文本
- 理由字段简要说明为什么这样控制

示例：
用户："我现在的猪肉价格是20块一斤请你综合利润和购买意愿进行定价"
回复：
{
  "价签终端": {
    "商品名称": "猪肉",
    "显示类型": "价格",
    "内容": "18.5元/斤",
    "理由": "综合考虑利润和购买意愿，适度降价以吸引更多顾客，同时保持合理利润"
  }
}

只返回JSON格式，不要添加任何其他文字说明或代码块标记。` :
                `你是智能零售标签系统的AI助手，专门帮助用户进行价格调控、市场分析和零售策略优化。请提供专业、准确、有洞察力的建议。当前时间：${new Date().toLocaleString()}`;

            const messages = [
                {
                    role: 'system',
                    content: systemPrompt
                },
                ...this.chatHistory.slice(-10), // 保留最近10轮对话
                { role: 'user', content: message }
            ];

            // 使用流式响应
            await this.callGLMApiStream(messages, message);
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addChatMessage('system', `❌ 发送失败: ${error.message}`);
        } finally {
            this.hideAiTyping();
        }
    }

    // 调用GLM API（保留非流式版本用于控制模式）
    async callGLMApi(messages) {
        const response = await fetch(this.aiConfig.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.aiConfig.apiKey}`
            },
            body: JSON.stringify({
                model: this.aiConfig.model,
                messages: messages,
                temperature: 0.7,
                max_tokens: 2000,
                stream: false
            })
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    // 流式调用GLM API
    async callGLMApiStream(messages, userMessage) {
        const response = await fetch(this.aiConfig.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.aiConfig.apiKey}`
            },
            body: JSON.stringify({
                model: this.aiConfig.model,
                messages: messages,
                temperature: 0.7,
                max_tokens: 2000,
                stream: true
            })
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        // 创建AI消息容器
        const messageDiv = this.createStreamingMessageContainer();
        let fullContent = '';

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            break;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                                const content = parsed.choices[0].delta.content || '';
                                if (content) {
                                    fullContent += content;
                                    this.updateStreamingMessage(messageDiv, fullContent);
                                }
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            }

            // 流式完成后的处理
            this.finalizeStreamingMessage(messageDiv, fullContent, userMessage);

        } catch (error) {
            console.error('流式读取错误:', error);
            this.updateStreamingMessage(messageDiv, '❌ 接收消息时出错: ' + error.message);
        }
    }

    // 创建流式消息容器
    createStreamingMessageContainer() {
        if (!this.elements.chatMessages) return null;

        // 移除欢迎消息
        const welcomeMsg = this.elements.chatMessages.querySelector('.welcome-message');
        if (welcomeMsg) {
            welcomeMsg.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message assistant streaming';

        const avatar = document.createElement('div');
        avatar.className = 'ai-avatar';
        avatar.textContent = '🤖';

        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';

        const content = document.createElement('div');
        content.className = 'streaming-content';
        content.innerHTML = '<span class="cursor">|</span>';

        bubble.appendChild(content);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(bubble);

        this.elements.chatMessages.appendChild(messageDiv);
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;

        return messageDiv;
    }

    // 更新流式消息内容
    updateStreamingMessage(messageDiv, content) {
        if (!messageDiv) return;

        const contentDiv = messageDiv.querySelector('.streaming-content');
        if (contentDiv) {
            contentDiv.innerHTML = this.formatMessageContent(content) + '<span class="cursor">|</span>';
            this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
        }
    }

    // 完成流式消息
    finalizeStreamingMessage(messageDiv, fullContent, userMessage) {
        if (!messageDiv) return;

        // 移除流式样式和光标
        messageDiv.classList.remove('streaming');
        const contentDiv = messageDiv.querySelector('.streaming-content');
        if (contentDiv) {
            contentDiv.className = 'message-content';

            // 检查是否为控制模式且返回JSON格式
            if (this.isControlMode && this.isJsonResponse(fullContent)) {
                this.handleControlResponse(fullContent);
                // 移除这个消息，因为会显示为控制卡片
                messageDiv.remove();
                return;
            } else {
                contentDiv.innerHTML = this.formatMessageContent(fullContent);
            }
        }

        // 添加时间戳
        const time = document.createElement('div');
        time.className = 'message-time';
        time.textContent = new Date().toLocaleTimeString();
        messageDiv.querySelector('.message-bubble').appendChild(time);

        // 添加到聊天历史
        this.chatHistory.push({ role: 'assistant', content: fullContent });

        // 分析消息内容并更新分析面板
        if (!this.isControlMode) {
            this.analyzeMessageContent(userMessage, fullContent);
        }

        // 更新API调用计数
        this.apiCallCount++;
        localStorage.setItem('smartRetail_apiCallCount', this.apiCallCount.toString());
        this.updateAiStatus();
    }

    // 添加聊天消息
    addChatMessage(role, content) {
        if (!this.elements.chatMessages) return;

        // 移除欢迎消息
        const welcomeMsg = this.elements.chatMessages.querySelector('.welcome-message');
        if (welcomeMsg) {
            welcomeMsg.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${role}`;

        const avatar = document.createElement('div');
        avatar.className = 'ai-avatar';
        avatar.textContent = role === 'user' ? '👤' : '🤖';

        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';
        bubble.innerHTML = this.formatMessageContent(content);

        const time = document.createElement('div');
        time.className = 'message-time';
        time.textContent = new Date().toLocaleTimeString();

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(bubble);
        bubble.appendChild(time);

        this.elements.chatMessages.appendChild(messageDiv);
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;

        // 添加到聊天历史
        if (role !== 'system') {
            this.chatHistory.push({ role, content });
        }
    }

    // 格式化消息内容
    formatMessageContent(content) {
        // 简单的Markdown格式支持
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    // 显示AI正在输入
    showAiTyping() {
        if (!this.elements.chatMessages) return;

        this.isAiTyping = true;

        const typingDiv = document.createElement('div');
        typingDiv.className = 'chat-message assistant typing-message';
        typingDiv.innerHTML = `
            <div class="ai-avatar">🤖</div>
            <div class="typing-indicator">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;

        this.elements.chatMessages.appendChild(typingDiv);
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    // 隐藏AI正在输入
    hideAiTyping() {
        this.isAiTyping = false;
        const typingMsg = this.elements.chatMessages?.querySelector('.typing-message');
        if (typingMsg) {
            typingMsg.remove();
        }
    }

    // 分析消息内容并更新分析面板
    analyzeMessageContent(userMessage, aiResponse) {
        if (!this.elements.analysisContent) return;

        // 检测关键词并生成分析结果
        const keywords = {
            '价格': '价格策略',
            '定价': '定价策略',
            '促销': '促销活动',
            '库存': '库存管理',
            '市场': '市场分析',
            '竞争': '竞争分析',
            '利润': '利润优化',
            '成本': '成本控制'
        };

        const detectedTags = [];
        const lowerMessage = userMessage.toLowerCase();

        Object.keys(keywords).forEach(keyword => {
            if (lowerMessage.includes(keyword)) {
                detectedTags.push(keywords[keyword]);
            }
        });

        if (detectedTags.length === 0) {
            detectedTags.push('一般咨询');
        }

        const analysisHtml = `
            <div class="analysis-result">
                <h4>💡 AI分析摘要</h4>
                <div class="result-content">
                    <p><strong>用户问题：</strong>${userMessage}</p>
                    <p><strong>分析要点：</strong></p>
                    <ul>
                        ${this.extractKeyPoints(aiResponse)}
                    </ul>
                </div>
                <div class="result-tags">
                    ${detectedTags.map(tag => `<span class="result-tag">${tag}</span>`).join('')}
                </div>
            </div>
        `;

        this.elements.analysisContent.innerHTML = analysisHtml;
    }

    // 提取AI回复的关键点
    extractKeyPoints(aiResponse) {
        const sentences = aiResponse.split(/[。！？.!?]/).filter(s => s.trim().length > 10);
        const keyPoints = sentences.slice(0, 3).map(point => `<li>${point.trim()}</li>`);
        return keyPoints.join('');
    }

    // 清空聊天历史
    clearChatHistory() {
        if (!this.elements.chatMessages) return;

        this.chatHistory = [];
        this.elements.chatMessages.innerHTML = `
            <div class="welcome-message">
                <div class="ai-avatar">🤖</div>
                <div class="message-content">
                    <h4>聊天记录已清空</h4>
                    <p>您可以开始新的对话了。</p>
                </div>
            </div>
        `;

        if (this.elements.analysisContent) {
            this.elements.analysisContent.innerHTML = `
                <div class="no-analysis">
                    <i class="fas fa-chart-bar"></i>
                    <p>暂无分析结果</p>
                    <small>与AI助手对话后，相关分析结果将在此显示</small>
                </div>
            `;
        }
    }

    // 导出聊天历史
    exportChatHistory() {
        if (this.chatHistory.length === 0) {
            this.showAiMessage('暂无聊天记录可导出', 'warning');
            return;
        }

        const exportData = {
            timestamp: new Date().toISOString(),
            model: this.aiConfig.model,
            totalMessages: this.chatHistory.length,
            conversations: this.chatHistory
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `ai-chat-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showAiMessage('聊天记录已导出', 'success');
    }

    // 更新AI状态显示
    updateAiStatus() {
        if (this.elements.apiStatus) {
            this.elements.apiStatus.textContent = this.aiConfig.apiKey ? '已配置' : '未配置';
            this.elements.apiStatus.className = `status-value ${this.aiConfig.apiKey ? 'status-connected' : 'status-disconnected'}`;
        }

        if (this.elements.currentModel) {
            this.elements.currentModel.textContent = this.aiConfig.apiKey ? this.aiConfig.model : '-';
        }

        if (this.elements.apiCallCount) {
            this.elements.apiCallCount.textContent = this.apiCallCount;
        }
    }

    // 更新聊天输入状态
    updateChatInputState() {
        const isConfigured = !!this.aiConfig.apiKey;

        if (this.elements.chatInput) {
            this.elements.chatInput.disabled = !isConfigured;
            this.elements.chatInput.placeholder = isConfigured ?
                '请输入您的问题或需求...' :
                '请先配置API密钥...';
        }

        if (this.elements.sendChatBtn) {
            this.elements.sendChatBtn.disabled = !isConfigured;
        }

        if (this.elements.voiceInputBtn) {
            this.elements.voiceInputBtn.disabled = !isConfigured;
        }
    }

    // 显示AI消息提示
    showAiMessage(message, type = 'info') {
        // 可以集成到现有的消息系统中
        this.addSystemMessage(`🤖 ${message}`);
    }

    // ==================== 智能控制模式功能 ====================

    // 切换控制模式
    toggleControlMode() {
        this.isControlMode = !this.isControlMode;

        // 更新UI状态
        if (this.elements.controlModeToggle) {
            this.elements.controlModeToggle.classList.toggle('active', this.isControlMode);
        }

        if (this.elements.modeIndicator) {
            this.elements.modeIndicator.textContent = this.isControlMode ? '控制模式' : '聊天模式';
            this.elements.modeIndicator.className = `control-mode-indicator ${this.isControlMode ? 'control' : 'chat'}`;
        }

        // 更新输入框提示
        if (this.elements.chatInput) {
            this.elements.chatInput.placeholder = this.isControlMode ?
                '请输入设备控制指令，如：把价格改成8.5元，显示买二赠一促销...' :
                '请输入您的问题或需求...';
        }

        // 添加模式切换提示
        this.addChatMessage('system', `🔄 已切换到${this.isControlMode ? '智能控制' : '聊天'}模式`);
    }

    // 检查是否为JSON响应
    isJsonResponse(text) {
        try {
            const trimmed = text.trim();

            // 检查是否为代码块包裹的JSON
            if (trimmed.includes('```json') || trimmed.includes('`json')) {
                return true;
            }

            // 检查是否为直接的JSON格式
            return (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
                   (trimmed.startsWith('[') && trimmed.endsWith(']'));
        } catch (error) {
            return false;
        }
    }

    // 从文本中提取JSON内容
    extractJsonFromText(text) {
        const trimmed = text.trim();

        // 处理```json代码块
        if (trimmed.includes('```json')) {
            const match = trimmed.match(/```json\s*([\s\S]*?)\s*```/);
            if (match) {
                return match[1].trim();
            }
        }

        // 处理`json代码块
        if (trimmed.includes('`json')) {
            const match = trimmed.match(/`json\s*([\s\S]*?)\s*`/);
            if (match) {
                return match[1].trim();
            }
        }

        // 尝试提取大括号内的内容
        const braceMatch = trimmed.match(/\{[\s\S]*\}/);
        if (braceMatch) {
            return braceMatch[0];
        }

        // 如果都没有匹配，返回原文本
        return trimmed;
    }

    // 处理控制响应
    handleControlResponse(jsonText) {
        try {
            // 提取JSON内容（处理代码块包裹的情况）
            const extractedJson = this.extractJsonFromText(jsonText);
            const controlData = JSON.parse(extractedJson);

            // 检查是否为单个控制指令
            if (controlData['价签终端']) {
                this.displayControlCard([controlData]);
            }
            // 检查是否为多个控制指令数组
            else if (Array.isArray(controlData)) {
                this.displayControlCard(controlData);
            }
            // 其他格式尝试解析
            else {
                this.displayControlCard([controlData]);
            }

        } catch (error) {
            console.error('解析控制指令失败:', error);
            this.addChatMessage('system', `❌ 控制指令格式错误: ${error.message}`);
            this.addChatMessage('assistant', jsonText); // 降级显示原文本
        }
    }

    // 显示控制指令卡片
    displayControlCard(controlCommands) {
        if (!this.elements.chatMessages) return;

        // 移除欢迎消息
        const welcomeMsg = this.elements.chatMessages.querySelector('.welcome-message');
        if (welcomeMsg) {
            welcomeMsg.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message assistant';

        const avatar = document.createElement('div');
        avatar.className = 'ai-avatar';
        avatar.textContent = '🤖';

        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';

        // 创建控制指令容器
        const commandsContainer = document.createElement('div');
        commandsContainer.className = 'control-commands-container';

        // 处理每个控制指令
        controlCommands.forEach((command, index) => {
            const terminal = command['价签终端'];
            if (terminal) {
                const cardHtml = this.createControlCardHtml(terminal, index + 1);
                commandsContainer.innerHTML += cardHtml;
            }
        });

        const time = document.createElement('div');
        time.className = 'message-time';
        time.textContent = new Date().toLocaleTimeString();

        bubble.appendChild(commandsContainer);
        bubble.appendChild(time);

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(bubble);

        this.elements.chatMessages.appendChild(messageDiv);
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;

        // 添加到聊天历史（保存原始JSON）
        this.chatHistory.push({
            role: 'assistant',
            content: JSON.stringify(controlCommands, null, 2)
        });

        // 更新分析面板
        this.analyzeControlCommand(controlCommands);
    }

    // 创建控制卡片HTML
    createControlCardHtml(terminal, index) {
        const displayTypeIcon = {
            '价格': '💰',
            '促销信息': '🎉',
            '库存状态': '📦'
        };

        const icon = displayTypeIcon[terminal.显示类型] || '🏷️';
        const productName = terminal.商品名称 || '未指定商品';

        return `
            <div class="control-command-card">
                <div class="card-header">
                    <span class="card-icon">${icon}</span>
                    <h4 class="card-title">${productName} - 价签控制指令 #${index}</h4>
                </div>
                <div class="card-body">
                    <div class="control-field">
                        <div class="control-field-label">商品名称</div>
                        <div class="control-field-value">${productName}</div>
                    </div>
                    <div class="control-field">
                        <div class="control-field-label">显示类型</div>
                        <div class="control-field-value">${terminal.显示类型}</div>
                    </div>
                    <div class="control-field">
                        <div class="control-field-label">显示内容</div>
                        <div class="control-field-value">${terminal.内容}</div>
                    </div>
                    <div class="control-reason">
                        <div class="control-field-label">控制理由</div>
                        <div class="control-field-value">${terminal.理由}</div>
                    </div>
                </div>
            </div>
        `;
    }

    // 分析控制指令
    analyzeControlCommand(controlCommands) {
        if (!this.elements.analysisContent) return;

        const commandTypes = controlCommands.map(cmd => cmd['价签终端']?.显示类型).filter(Boolean);
        const uniqueTypes = [...new Set(commandTypes)];

        const analysisHtml = `
            <div class="analysis-result">
                <h4>🎛️ 控制指令分析</h4>
                <div class="result-content">
                    <p><strong>指令数量：</strong>${controlCommands.length} 条</p>
                    <p><strong>涉及类型：</strong>${uniqueTypes.join('、')}</p>
                    <p><strong>执行时间：</strong>${new Date().toLocaleString()}</p>
                </div>
                <div class="result-tags">
                    ${uniqueTypes.map(type => `<span class="result-tag">${type}</span>`).join('')}
                    <span class="result-tag">智能控制</span>
                </div>
            </div>
        `;

        this.elements.analysisContent.innerHTML = analysisHtml;
    }

    // ==================== 显示调整界面功能 ====================

    // 初始化显示调整界面
    initDisplayInterface() {
        // 重新获取DOM元素，确保元素已加载
        this.elements.deviceGrid = document.getElementById('deviceGrid');
        this.elements.statusFilter = document.getElementById('statusFilter');
        this.elements.cityFilter = document.getElementById('cityFilter');
        this.elements.searchInput = document.getElementById('searchInput');

        this.initCitiesList();
        this.updateDisplayStats();
        this.filterDevices();
    }

    // 初始化中国地级市列表
    initCitiesList() {
        this.cities = [
            '北京市', '上海市', '天津市', '重庆市',
            '广东广州', '广东深圳', '广东东莞', '广东佛山', '广东中山', '广东珠海',
            '江苏南京', '江苏苏州', '江苏无锡', '江苏常州', '江苏南通', '江苏徐州',
            '浙江杭州', '浙江宁波', '浙江温州', '浙江嘉兴', '浙江台州', '浙江金华',
            '山东济南', '山东青岛', '山东烟台', '山东潍坊', '山东临沂', '山东济宁',
            '河南郑州', '河南洛阳', '河南新乡', '河南焦作', '河南安阳', '河南开封',
            '湖北武汉', '湖北宜昌', '湖北襄阳', '湖北荆州', '湖北黄石', '湖北十堰',
            '湖南长沙', '湖南株洲', '湖南湘潭', '湖南衡阳', '湖南岳阳', '湖南常德',
            '四川成都', '四川绵阳', '四川德阳', '四川南充', '四川宜宾', '四川自贡',
            '河北石家庄', '河北唐山', '河北邯郸', '河北保定', '河北沧州', '河北邢台',
            '福建福州', '福建厦门', '福建泉州', '福建漳州', '福建莆田', '福建三明',
            '安徽合肥', '安徽芜湖', '安徽蚌埠', '安徽阜阳', '安徽淮南', '安徽马鞍山',
            '辽宁沈阳', '辽宁大连', '辽宁鞍山', '辽宁抚顺', '辽宁本溪', '辽宁丹东',
            '陕西西安', '陕西宝鸡', '陕西咸阳', '陕西渭南', '陕西汉中', '陕西榆林',
            '江西南昌', '江西赣州', '江西九江', '江西宜春', '江西吉安', '江西上饶'
        ];

        // 填充城市筛选下拉框
        if (this.elements.cityFilter) {
            this.cities.forEach(city => {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                this.elements.cityFilter.appendChild(option);
            });
        }
    }

    // 检查是否为设备数量数据格式
    isDeviceNumberData(data) {
        const pattern = /^device_number:\d+$/;
        return pattern.test(data.trim());
    }

    // 处理设备数量消息
    handleDeviceNumberMessage(data) {
        const match = data.trim().match(/^device_number:(\d+)$/);
        if (match) {
            const deviceCount = parseInt(match[1]);
            this.generateDevices(deviceCount);
            this.addSystemMessage(`📱 已生成 ${deviceCount} 个设备，可在显示调整界面进行配置`);
        }
    }

    // 生成设备列表
    generateDevices(count) {
        this.devices.clear();
        this.savedDevices.clear();

        for (let i = 1; i <= count; i++) {
            const deviceId = `device_${i.toString().padStart(3, '0')}`;
            this.devices.set(deviceId, {
                id: deviceId,
                productName: '',
                manufacturer: '',
                specifications: '',
                barcode: '',
                retailPrice: '',
                memberPrice: '',
                status: 'pending' // pending, saved, updated
            });
        }

        this.currentPage = 1;
        this.updateDisplayStats();
        this.filterDevices();
    }

    // 更新显示统计信息
    updateDisplayStats() {
        const total = this.devices.size;

        // 正确计算各种状态的设备数量
        let savedCount = 0;
        let pendingCount = 0;
        let updatedCount = 0;

        this.devices.forEach(device => {
            switch (device.status) {
                case 'saved':
                    savedCount++;
                    break;
                case 'pending':
                    pendingCount++;
                    break;
                case 'updated':
                    updatedCount++;
                    break;
            }
        });

        if (this.elements.totalDevices) {
            this.elements.totalDevices.textContent = total;
        }

        if (this.elements.savedDevices) {
            this.elements.savedDevices.textContent = savedCount;
        }

        if (this.elements.pendingDevices) {
            this.elements.pendingDevices.textContent = pendingCount;
        }

        // 更新批量更新按钮状态（只有已保存的设备才能批量更新）
        if (this.elements.batchUpdateBtn) {
            this.elements.batchUpdateBtn.disabled = savedCount === 0;
        }

        // 同步更新savedDevices集合，只包含状态为'saved'的设备
        this.savedDevices.clear();
        this.devices.forEach((device, deviceId) => {
            if (device.status === 'saved') {
                this.savedDevices.add(deviceId);
            }
        });
    }

    // 筛选设备
    filterDevices() {
        const searchTerm = this.elements.searchInput?.value.toLowerCase() || '';
        const statusFilter = this.elements.statusFilter?.value || 'all';
        const cityFilter = this.elements.cityFilter?.value || 'all';

        this.filteredDevices = Array.from(this.devices.values()).filter(device => {
            // 搜索筛选
            const matchesSearch = !searchTerm ||
                (device.productName || '').toLowerCase().includes(searchTerm) ||
                (device.barcode || '').toLowerCase().includes(searchTerm) ||
                (device.manufacturer || '').toLowerCase().includes(searchTerm);

            // 状态筛选
            const matchesStatus = statusFilter === 'all' || device.status === statusFilter;

            // 城市筛选
            const matchesCity = cityFilter === 'all' || device.manufacturer === cityFilter;

            return matchesSearch && matchesStatus && matchesCity;
        });

        this.currentPage = 1;
        this.renderDeviceGrid();
    }

    // 渲染设备网格
    renderDeviceGrid() {
        if (!this.elements.deviceGrid) return;

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageDevices = this.filteredDevices.slice(startIndex, endIndex);

        if (pageDevices.length === 0) {
            this.elements.deviceGrid.innerHTML = `
                <div class="no-devices">
                    <i class="fas fa-inbox"></i>
                    <p>暂无设备信息</p>
                    <small>等待接收设备数量信息...</small>
                </div>
            `;
        } else {
            this.elements.deviceGrid.innerHTML = pageDevices.map(device =>
                this.createDeviceCard(device)
            ).join('');
        }

        this.updatePagination();
        this.updateDisplayedCount();
    }

    // 创建设备卡片HTML
    createDeviceCard(device) {
        const statusClass = device.status;
        const statusText = {
            'pending': '待更新',
            'saved': '已保存',
            'updated': '已更新'
        }[device.status] || '待更新';

        return `
            <div class="device-card ${statusClass}" data-device-id="${device.id}">
                <div class="device-card-header">
                    <span class="device-id">${device.id}</span>
                    <span class="device-status ${statusClass}">${statusText}</span>
                </div>
                <form class="device-form" onsubmit="return false;">
                    <div class="form-group">
                        <label>商品名称</label>
                        <input type="text" name="productName" value="${device.productName}"
                               placeholder="请输入商品名称">
                    </div>
                    <div class="form-group">
                        <label>厂地</label>
                        <select name="manufacturer">
                            <option value="">请选择厂地</option>
                            ${this.cities.map(city =>
                                `<option value="${city}" ${device.manufacturer === city ? 'selected' : ''}>${city}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>规格</label>
                        <input type="text" name="specifications" value="${device.specifications}"
                               placeholder="请输入规格">
                    </div>
                    <div class="form-group">
                        <label>条码</label>
                        <input type="text" name="barcode" value="${device.barcode}"
                               placeholder="请输入条码">
                    </div>
                    <div class="form-group">
                        <label>零售价</label>
                        <input type="number" name="retailPrice" value="${device.retailPrice}"
                               placeholder="0.00" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label>会员价</label>
                        <input type="number" name="memberPrice" value="${device.memberPrice}"
                               placeholder="0.00" step="0.01" min="0">
                    </div>
                </form>
                <div class="device-actions">
                    <button class="btn btn-primary btn-sm" onclick="smartRetailSystem.updateDevice('${device.id}')">
                        <i class="fas fa-sync"></i> 更新
                    </button>
                    <button class="btn btn-outline btn-sm" onclick="smartRetailSystem.saveDevice('${device.id}')">
                        <i class="fas fa-save"></i> 保存
                    </button>
                </div>
            </div>
        `;
    }

    // 更新分页信息
    updatePagination() {
        const totalPages = Math.ceil(this.filteredDevices.length / this.itemsPerPage);

        if (this.elements.currentPageSpan) {
            this.elements.currentPageSpan.textContent = this.currentPage;
        }

        if (this.elements.totalPages) {
            this.elements.totalPages.textContent = totalPages;
        }

        if (this.elements.prevPageBtn) {
            this.elements.prevPageBtn.disabled = this.currentPage <= 1;
        }

        if (this.elements.nextPageBtn) {
            this.elements.nextPageBtn.disabled = this.currentPage >= totalPages;
        }
    }

    // 更新显示数量
    updateDisplayedCount() {
        if (this.elements.displayedCount) {
            this.elements.displayedCount.textContent = this.filteredDevices.length;
        }
    }

    // 保存设备信息
    saveDevice(deviceId) {
        const deviceCard = document.querySelector(`[data-device-id="${deviceId}"]`);
        if (!deviceCard) return;

        const form = deviceCard.querySelector('.device-form');
        const formData = new FormData(form);
        const device = this.devices.get(deviceId);

        if (device) {
            // 更新设备信息
            device.productName = formData.get('productName') || '';
            device.manufacturer = formData.get('manufacturer') || '';
            device.specifications = formData.get('specifications') || '';
            device.barcode = formData.get('barcode') || '';
            device.retailPrice = formData.get('retailPrice') || '';
            device.memberPrice = formData.get('memberPrice') || '';
            device.status = 'saved';

            // 添加到已保存列表
            this.savedDevices.add(deviceId);

            // 更新UI
            this.updateDisplayStats();
            this.renderDeviceGrid();

            this.addSystemMessage(`💾 设备 ${deviceId} 信息已保存`);
        }
    }

    // 更新单个设备
    updateDevice(deviceId) {
        const deviceCard = document.querySelector(`[data-device-id="${deviceId}"]`);
        if (!deviceCard) return;

        const form = deviceCard.querySelector('.device-form');
        const formData = new FormData(form);
        const device = this.devices.get(deviceId);

        if (device) {
            // 更新设备信息
            device.productName = formData.get('productName') || '';
            device.manufacturer = formData.get('manufacturer') || '';
            device.specifications = formData.get('specifications') || '';
            device.barcode = formData.get('barcode') || '';
            device.retailPrice = formData.get('retailPrice') || '';
            device.memberPrice = formData.get('memberPrice') || '';
            device.status = 'updated';

            // 生成JSON格式的更新指令
            const updateCommand = {
                deviceId: deviceId,
                productInfo: {
                    productName: device.productName,
                    manufacturer: device.manufacturer,
                    specifications: device.specifications,
                    barcode: device.barcode,
                    retailPrice: parseFloat(device.retailPrice) || 0,
                    memberPrice: parseFloat(device.memberPrice) || 0
                },
                timestamp: new Date().toISOString()
            };

            // 发送更新指令
            this.sendUpdateCommand(updateCommand);

            // 更新UI
            this.updateDisplayStats(); // 添加统计信息更新
            this.renderDeviceGrid();

            this.addSystemMessage(`🔄 设备 ${deviceId} 已发送更新指令`);
        }
    }

    // 发送更新指令
    sendUpdateCommand(command) {
        if (this.isConnected && this.ws) {
            try {
                this.ws.send(JSON.stringify(command));
                console.log('发送设备更新指令:', command);
            } catch (error) {
                console.error('发送更新指令失败:', error);
                this.addSystemMessage(`❌ 发送更新指令失败: ${error.message}`);
            }
        } else {
            console.log('模拟发送更新指令:', command);
            this.addSystemMessage(`📡 模拟发送更新指令 (WebSocket未连接)`);
        }
    }

    // 批量更新设备
    batchUpdateDevices() {
        const savedDevicesList = Array.from(this.savedDevices);
        if (savedDevicesList.length === 0) {
            this.addSystemMessage('❌ 没有已保存的设备可以更新');
            return;
        }

        // 显示确认弹窗
        this.showBatchUpdateModal(savedDevicesList);
    }

    // 显示批量更新确认弹窗
    showBatchUpdateModal(deviceIds) {
        const modal = document.createElement('div');
        modal.className = 'update-modal show';
        modal.innerHTML = `
            <div class="update-modal-content">
                <h3>🔄 批量更新确认</h3>
                <div class="update-summary">
                    <p><strong>即将更新设备数量:</strong> ${deviceIds.length} 个</p>
                    <p><strong>更新时间:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>操作说明:</strong> 将向所有已保存的设备发送更新指令</p>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-outline" onclick="this.closest('.update-modal').remove()">取消</button>
                    <button class="btn btn-primary" onclick="smartRetailSystem.confirmBatchUpdate(['${deviceIds.join("','")}']); this.closest('.update-modal').remove();">确认更新</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    // 确认批量更新
    confirmBatchUpdate(deviceIds) {
        let successCount = 0;

        deviceIds.forEach(deviceId => {
            const device = this.devices.get(deviceId);
            if (device) {
                // 生成更新指令
                const updateCommand = {
                    deviceId: deviceId,
                    productInfo: {
                        productName: device.productName,
                        manufacturer: device.manufacturer,
                        specifications: device.specifications,
                        barcode: device.barcode,
                        retailPrice: parseFloat(device.retailPrice) || 0,
                        memberPrice: parseFloat(device.memberPrice) || 0
                    },
                    timestamp: new Date().toISOString()
                };

                // 发送更新指令
                this.sendUpdateCommand(updateCommand);

                // 更新设备状态
                device.status = 'updated';
                successCount++;
            }
        });

        // 更新UI
        this.renderDeviceGrid();
        this.updateDisplayStats();

        this.addSystemMessage(`✅ 批量更新完成，成功更新 ${successCount} 个设备`);
    }

    // 清空所有设备
    clearAllDevices() {
        if (this.devices.size === 0) {
            this.addSystemMessage('❌ 没有设备可以清空');
            return;
        }

        if (confirm(`确定要清空所有 ${this.devices.size} 个设备吗？此操作不可撤销。`)) {
            this.devices.clear();
            this.savedDevices.clear();
            this.filteredDevices = [];
            this.currentPage = 1;

            this.updateDisplayStats();
            this.renderDeviceGrid();

            this.addSystemMessage('🗑️ 已清空所有设备信息');
        }
    }

    // 导出设备数据
    exportDeviceData() {
        if (this.devices.size === 0) {
            this.addSystemMessage('❌ 没有设备数据可以导出');
            return;
        }

        const exportData = {
            timestamp: new Date().toISOString(),
            totalDevices: this.devices.size,
            savedDevices: this.savedDevices.size,
            devices: Array.from(this.devices.values())
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `device-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.addSystemMessage('📥 设备数据已导出');
    }

    // 搜索设备
    searchDevices() {
        this.filterDevices();
    }

    // 清空搜索
    clearSearch() {
        if (this.elements.searchInput) {
            this.elements.searchInput.value = '';
        }
        if (this.elements.statusFilter) {
            this.elements.statusFilter.value = 'all';
        }
        if (this.elements.cityFilter) {
            this.elements.cityFilter.value = 'all';
        }
        this.filterDevices();
    }

    // 上一页
    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.renderDeviceGrid();
        }
    }

    // 下一页
    nextPage() {
        const totalPages = Math.ceil(this.filteredDevices.length / this.itemsPerPage);
        if (this.currentPage < totalPages) {
            this.currentPage++;
            this.renderDeviceGrid();
        }
    }
}

// 页面加载完成后初始化系统
document.addEventListener('DOMContentLoaded', () => {
    window.smartRetailSystem = new SmartRetailSystem();
});

// 前端HTTP服务器
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// 配置
const config = {
    port: process.env.HTTP_PORT || 3000, // HTTP服务器端口
    host: process.env.HTTP_HOST || 'localhost', // HTTP服务器地址
    frontendDir: path.join(__dirname, 'frontend') // 前端文件目录
};

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html; charset=utf-8',
    '.css': 'text/css; charset=utf-8',
    '.js': 'application/javascript; charset=utf-8',
    '.json': 'application/json; charset=utf-8',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.txt': 'text/plain; charset=utf-8'
};

// 获取文件MIME类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

// 发送文件响应
function sendFile(res, filePath) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            console.error('读取文件失败:', filePath, err.message);
            res.writeHead(500, { 'Content-Type': 'text/plain; charset=utf-8' });
            res.end('500 - 服务器内部错误');
            return;
        }
        
        const mimeType = getMimeType(filePath);
        res.writeHead(200, { 
            'Content-Type': mimeType,
            'Cache-Control': 'no-cache' // 开发时禁用缓存
        });
        res.end(data);
    });
}

// 发送404响应
function send404(res) {
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>404 - 页面未找到</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                h1 { color: #e74c3c; }
                a { color: #3498db; text-decoration: none; }
                a:hover { text-decoration: underline; }
            </style>
        </head>
        <body>
            <h1>404 - 页面未找到</h1>
            <p>您访问的页面不存在</p>
            <a href="/">返回首页</a>
        </body>
        </html>
    `);
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // 记录请求
    console.log(`[${new Date().toISOString()}] ${req.method} ${pathname}`);
    
    // 处理根路径，重定向到index.html
    if (pathname === '/') {
        pathname = '/index.html';
    }
    
    // 构建文件路径
    const filePath = path.join(config.frontendDir, pathname);
    
    // 安全检查：确保文件在frontend目录内
    const normalizedPath = path.normalize(filePath);
    if (!normalizedPath.startsWith(config.frontendDir)) {
        console.warn('安全警告: 尝试访问目录外文件:', pathname);
        send404(res);
        return;
    }
    
    // 检查文件是否存在
    fs.stat(filePath, (err, stats) => {
        if (err || !stats.isFile()) {
            send404(res);
            return;
        }
        
        sendFile(res, filePath);
    });
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${config.port} 已被占用，请尝试其他端口`);
        console.error(`   可以设置环境变量: HTTP_PORT=3001`);
    } else {
        console.error('❌ HTTP服务器错误:', err.message);
    }
    process.exit(1);
});

// 启动服务器
server.listen(config.port, config.host, () => {
    console.log('\n🌐 前端HTTP服务器启动成功');
    console.log(`📡 服务器地址: http://${config.host}:${config.port}`);
    console.log(`📁 静态文件目录: ${config.frontendDir}`);
    console.log(`🔗 访问地址: http://${config.host}:${config.port}`);
    console.log('📊 服务器状态: 等待HTTP请求...\n');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n📴 收到关闭信号，正在关闭HTTP服务器...');
    server.close(() => {
        console.log('✅ HTTP服务器已安全关闭');
        process.exit(0);
    });
});

module.exports = server;

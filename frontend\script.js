// 智能零售标签系统 - 前端脚本文件

class SmartRetailSystem {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
        this.reconnectTimer = null;
        this.connectionTimer = null;
        this.connectionStartTime = null;
        this.sentCount = 0;
        this.receivedCount = 0;
        this.connectedAddress = '';
        this.currentPage = 'home';
        this.map = null;
        this.markers = new Map(); // 存储设备标记
        this.deviceList = new Map(); // 存储设备信息

        this.initElements();
        this.bindEvents();
        this.loadConfig();
        this.initNavigation();
    }
    
    // 初始化DOM元素引用
    initElements() {
        console.log('正在初始化DOM元素...');
        this.elements = {
            // 导航元素
            navItems: document.querySelectorAll('.nav-item'),
            pageContents: document.querySelectorAll('.page-content'),
            
            // 连接相关元素
            connectBtn: document.getElementById('connectBtn'),
            disconnectBtn: document.getElementById('disconnectBtn'),
            connectionDot: document.getElementById('connectionDot'),
            connectionText: document.getElementById('connectionText'),
            connectionModal: document.getElementById('connectionModal'),
            modalClose: document.getElementById('modalClose'),
            modalCancel: document.getElementById('modalCancel'),
            modalConnect: document.getElementById('modalConnect'),
            modalServerAddress: document.getElementById('modalServerAddress'),
            
            // 终端页面元素
            serverAddress: document.getElementById('serverAddress'),
            connectionStatus: document.getElementById('connectionStatus'),
            serverAddressDisplay: document.getElementById('serverAddressDisplay'),
            clientId: document.getElementById('clientId'),
            messageContainer: document.getElementById('messageContainer'),
            messageInput: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            clearBtn: document.getElementById('clearBtn'),
            sentCount: document.getElementById('sentCount'),
            receivedCount: document.getElementById('receivedCount'),
            connectionTime: document.getElementById('connectionTime'),
            lastActivity: document.getElementById('lastActivity'),

            // 定位界面元素
            mapContainer: document.getElementById('mapContainer'),
            centerMapBtn: document.getElementById('centerMapBtn'),
            clearMarkersBtn: document.getElementById('clearMarkersBtn'),
            refreshMapBtn: document.getElementById('refreshMapBtn'),
            onlineDevices: document.getElementById('onlineDevices'),
            totalMarkers: document.getElementById('totalMarkers'),
            lastLocationUpdate: document.getElementById('lastLocationUpdate'),
            deviceList: document.getElementById('deviceList')
        };

        console.log('DOM元素初始化完成:', {
            navItems: this.elements.navItems.length,
            connectBtn: !!this.elements.connectBtn,
            pageContents: this.elements.pageContents.length
        });
    }
    
    // 绑定事件监听器
    bindEvents() {
        // 导航事件
        this.elements.navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const page = e.currentTarget.dataset.page;
                this.switchPage(page);
            });
        });
        
        // 连接按钮事件
        if (this.elements.connectBtn) {
            this.elements.connectBtn.addEventListener('click', () => this.showConnectionModal());
        }
        
        if (this.elements.disconnectBtn) {
            this.elements.disconnectBtn.addEventListener('click', () => this.disconnect());
        }
        
        // 模态框事件
        if (this.elements.modalClose) {
            this.elements.modalClose.addEventListener('click', () => this.hideConnectionModal());
        }
        
        if (this.elements.modalCancel) {
            this.elements.modalCancel.addEventListener('click', () => this.hideConnectionModal());
        }
        
        if (this.elements.modalConnect) {
            this.elements.modalConnect.addEventListener('click', () => this.connectFromModal());
        }
        
        // 消息发送事件
        if (this.elements.sendBtn) {
            this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        }
        
        if (this.elements.messageInput) {
            this.elements.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }
        
        // 清空消息事件
        if (this.elements.clearBtn) {
            this.elements.clearBtn.addEventListener('click', () => this.clearMessages());
        }
        
        // 模态框外部点击关闭
        if (this.elements.connectionModal) {
            this.elements.connectionModal.addEventListener('click', (e) => {
                if (e.target === this.elements.connectionModal) {
                    this.hideConnectionModal();
                }
            });
        }

        // 定位界面事件
        if (this.elements.centerMapBtn) {
            this.elements.centerMapBtn.addEventListener('click', () => this.centerMap());
        }

        if (this.elements.clearMarkersBtn) {
            this.elements.clearMarkersBtn.addEventListener('click', () => this.clearAllMarkers());
        }

        if (this.elements.refreshMapBtn) {
            this.elements.refreshMapBtn.addEventListener('click', () => this.refreshMap());
        }
    }
    
    // 初始化导航
    initNavigation() {
        this.switchPage('home');
    }
    
    // 切换页面
    switchPage(page) {
        // 更新导航状态
        this.elements.navItems.forEach(item => {
            item.classList.remove('active');
            if (item.dataset.page === page) {
                item.classList.add('active');
            }
        });
        
        // 更新页面内容
        this.elements.pageContents.forEach(content => {
            content.classList.remove('active');
            if (content.id === `${page}-page`) {
                content.classList.add('active');
            }
        });
        
        this.currentPage = page;

        // 如果切换到定位页面，初始化地图
        if (page === 'location' && !this.map) {
            setTimeout(() => this.initMap(), 100);
        }
    }
    
    // 显示连接模态框
    showConnectionModal() {
        if (this.elements.connectionModal) {
            this.elements.connectionModal.classList.add('active');
            if (this.elements.modalServerAddress) {
                this.elements.modalServerAddress.focus();
            }
        }
    }
    
    // 隐藏连接模态框
    hideConnectionModal() {
        if (this.elements.connectionModal) {
            this.elements.connectionModal.classList.remove('active');
        }
    }
    
    // 从模态框连接
    connectFromModal() {
        const address = this.elements.modalServerAddress?.value.trim();
        if (address) {
            this.connect(address);
            this.hideConnectionModal();
        }
    }
    
    // 连接WebSocket服务器
    connect(address = null) {
        if (this.isConnected) return;
        
        const serverAddress = address || this.elements.serverAddress?.value.trim() || 'localhost:8082';
        
        // 构建WebSocket URL
        let url;
        if (serverAddress.startsWith('ws://') || serverAddress.startsWith('wss://')) {
            url = serverAddress;
        } else {
            url = `ws://${serverAddress}`;
        }
        
        this.connectedAddress = serverAddress;
        this.updateConnectionStatus('connecting', '连接中...');
        this.addSystemMessage(`正在连接到 ${url}...`);
        
        try {
            this.ws = new WebSocket(url);
            this.setupWebSocketEvents();
        } catch (error) {
            this.handleConnectionError('连接失败: ' + error.message);
        }
    }
    
    // 设置WebSocket事件监听
    setupWebSocketEvents() {
        this.ws.onopen = () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.connectionStartTime = new Date();
            this.startConnectionTimer();
            
            console.log('WebSocket连接成功:', this.connectedAddress);
            console.log('WebSocket URL:', this.ws.url);
            this.updateConnectionStatus('connected', '已连接');
            if (this.elements.serverAddressDisplay) {
                this.elements.serverAddressDisplay.textContent = this.connectedAddress;
            }
            this.addSystemMessage(`✅ 成功连接到服务器: ${this.connectedAddress}`);
            this.addSystemMessage(`🔗 实际连接URL: ${this.ws.url}`);
            this.updateUI();
        };
        
        this.ws.onmessage = (event) => {
            console.log('收到WebSocket消息:', event.data);
            this.handleMessage(event.data);
            this.updateLastActivity();
        };
        
        this.ws.onclose = (event) => {
            this.isConnected = false;
            this.clearConnectionTimer();
            this.updateConnectionStatus('disconnected', '连接已断开');
            this.addSystemMessage('❌ 连接已断开');
            this.updateUI();
            
            if (this.reconnectAttempts < this.maxReconnectAttempts && !event.wasClean) {
                this.scheduleReconnect();
            }
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.addSystemMessage('❌ 连接错误');
        };
    }
    
    // 断开连接
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, '用户主动断开');
            this.clearReconnectTimer();
        }
    }
    
    // 处理接收到的消息
    handleMessage(data) {
        this.receivedCount++;
        if (this.elements.receivedCount) {
            this.elements.receivedCount.textContent = this.receivedCount;
        }

        // 检查是否为位置信息格式：经度_纬度
        if (this.isLocationData(data)) {
            this.handleLocationMessage(data);
            return;
        }

        try {
            const message = JSON.parse(data);
            if (message.type === 'welcome') {
                this.clientId = message.clientId;
                if (this.elements.clientId) {
                    this.elements.clientId.textContent = this.clientId;
                }
                this.addSystemMessage(`🆔 客户端ID: ${this.clientId}`);
            } else {
                this.addReceivedMessage(data);
            }
        } catch (error) {
            this.addReceivedMessage(data);
        }
    }
    
    // 发送消息
    sendMessage() {
        if (!this.isConnected || !this.ws) {
            this.addSystemMessage('❌ 请先连接到服务器');
            return;
        }
        
        const message = this.elements.messageInput?.value.trim();
        if (!message) return;
        
        try {
            console.log('发送消息:', message, '到服务器:', this.connectedAddress);
            console.log('WebSocket状态:', this.ws.readyState);
            this.ws.send(message);
            this.sentCount++;
            if (this.elements.sentCount) {
                this.elements.sentCount.textContent = this.sentCount;
            }
            this.addSentMessage(message);
            if (this.elements.messageInput) {
                this.elements.messageInput.value = '';
            }
            this.updateLastActivity();
            console.log('消息发送成功');
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addSystemMessage('❌ 发送消息失败: ' + error.message);
        }
    }
    
    // 更新连接状态
    updateConnectionStatus(status, text) {
        if (this.elements.connectionDot) {
            this.elements.connectionDot.className = 'status-dot';
            if (status === 'connected') {
                this.elements.connectionDot.classList.add('connected');
            }
        }
        
        if (this.elements.connectionText) {
            this.elements.connectionText.textContent = text;
        }
        
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.className = `status-value status-${status}`;
            this.elements.connectionStatus.textContent = text;
        }
    }
    
    // 更新界面状态
    updateUI() {
        if (this.elements.connectBtn) {
            this.elements.connectBtn.textContent = this.isConnected ? '已连接' : '连接系统';
            this.elements.connectBtn.disabled = this.isConnected;
        }

        if (this.elements.disconnectBtn) {
            this.elements.disconnectBtn.disabled = !this.isConnected;
        }

        if (this.elements.messageInput) {
            this.elements.messageInput.disabled = !this.isConnected;
        }

        if (this.elements.sendBtn) {
            this.elements.sendBtn.disabled = !this.isConnected;
        }

        if (!this.isConnected) {
            if (this.elements.serverAddressDisplay) {
                this.elements.serverAddressDisplay.textContent = '-';
            }
            if (this.elements.clientId) {
                this.elements.clientId.textContent = '-';
            }
        }
    }

    // 添加系统消息
    addSystemMessage(message) {
        this.addMessage(message, 'system');
    }

    // 添加发送的消息
    addSentMessage(message) {
        this.addMessage(`我: ${message}`, 'sent');
    }

    // 添加接收的消息
    addReceivedMessage(message) {
        this.addMessage(`收到: ${message}`, 'received');
    }

    // 添加消息到容器
    addMessage(content, type) {
        if (!this.elements.messageContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message-item message-${type}`;

        const contentDiv = document.createElement('div');
        contentDiv.textContent = content;
        messageDiv.appendChild(contentDiv);

        const timestampDiv = document.createElement('div');
        timestampDiv.className = 'message-timestamp';
        timestampDiv.textContent = new Date().toLocaleTimeString();
        messageDiv.appendChild(timestampDiv);

        this.elements.messageContainer.appendChild(messageDiv);
        this.elements.messageContainer.scrollTop = this.elements.messageContainer.scrollHeight;
    }

    // 清空消息
    clearMessages() {
        if (this.elements.messageContainer) {
            this.elements.messageContainer.innerHTML = `
                <div class="welcome-message">
                    <p>👋 欢迎使用智能标签管理系统终端！</p>
                    <p>请先连接到WebSocket服务器开始使用。</p>
                </div>
            `;
        }
    }

    // 开始连接时长计时
    startConnectionTimer() {
        this.connectionTimer = setInterval(() => {
            if (this.connectionStartTime && this.elements.connectionTime) {
                const duration = Date.now() - this.connectionStartTime.getTime();
                this.elements.connectionTime.textContent = this.formatDuration(duration);
            }
        }, 1000);
    }

    // 清除连接计时器
    clearConnectionTimer() {
        if (this.connectionTimer) {
            clearInterval(this.connectionTimer);
            this.connectionTimer = null;
        }
    }

    // 更新最后活动时间
    updateLastActivity() {
        if (this.elements.lastActivity) {
            this.elements.lastActivity.textContent = new Date().toLocaleTimeString();
        }
    }

    // 格式化持续时间
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        const h = hours.toString().padStart(2, '0');
        const m = (minutes % 60).toString().padStart(2, '0');
        const s = (seconds % 60).toString().padStart(2, '0');

        return `${h}:${m}:${s}`;
    }

    // 处理连接错误
    handleConnectionError(message) {
        this.updateConnectionStatus('disconnected', '连接失败');
        this.addSystemMessage(`❌ ${message}`);
        this.updateUI();
    }

    // 安排重连
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectInterval * this.reconnectAttempts;

        this.addSystemMessage(`🔄 ${delay/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

        this.reconnectTimer = setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }

    // 清除重连定时器
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        this.reconnectAttempts = 0;
    }

    // 加载配置
    loadConfig() {
        try {
            const savedAddress = localStorage.getItem('smartRetail_serverAddress');
            if (savedAddress) {
                if (this.elements.serverAddress) {
                    this.elements.serverAddress.value = savedAddress;
                }
                if (this.elements.modalServerAddress) {
                    this.elements.modalServerAddress.value = savedAddress;
                }
            }
        } catch (error) {
            console.warn('加载配置失败:', error);
        }
    }

    // 保存配置
    saveConfig() {
        try {
            const address = this.elements.serverAddress?.value || this.elements.modalServerAddress?.value;
            if (address) {
                localStorage.setItem('smartRetail_serverAddress', address);
            }
        } catch (error) {
            console.warn('保存配置失败:', error);
        }
    }

    // 检查是否为位置数据格式
    isLocationData(data) {
        const locationPattern = /^(\d+\.\d+)_(\d+\.\d+)$/;
        return locationPattern.test(data.trim());
    }

    // 处理位置消息
    handleLocationMessage(data) {
        const [longitude, latitude] = data.trim().split('_').map(Number);

        if (this.isValidCoordinate(longitude, latitude)) {
            const deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
            this.addDeviceLocation(deviceId, longitude, latitude);
            this.addSystemMessage(`📍 收到位置信息: ${longitude}, ${latitude}`);
        } else {
            this.addSystemMessage(`❌ 无效的位置信息: ${data}`);
        }
    }

    // 验证坐标有效性
    isValidCoordinate(lng, lat) {
        // 中国大陆经纬度范围大致：经度73-135，纬度18-54
        return lng >= 73 && lng <= 135 && lat >= 18 && lat <= 54;
    }

    // 初始化地图
    initMap() {
        if (!window.L) {
            this.showMapError('地图API加载失败，请检查网络连接');
            return;
        }

        try {
            // 创建地图实例
            this.map = L.map('mapContainer').setView([39.90923, 116.397428], 5);

            // 添加瓦片图层 - 使用OpenStreetMap
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(this.map);

            // 添加比例尺控件
            L.control.scale().addTo(this.map);

            // 隐藏加载提示
            const loadingEl = this.elements.mapContainer?.querySelector('.map-loading');
            if (loadingEl) {
                loadingEl.style.display = 'none';
            }

            console.log('地图初始化成功');
        } catch (error) {
            console.error('地图初始化失败:', error);
            this.showMapError('地图初始化失败: ' + error.message);
        }
    }

    // 显示地图错误
    showMapError(message) {
        if (this.elements.mapContainer) {
            this.elements.mapContainer.innerHTML = `
                <div class="map-loading">
                    <i class="fas fa-exclamation-triangle" style="color: #e74c3c;"></i>
                    <p style="color: #e74c3c;">${message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }

    // 添加设备位置
    addDeviceLocation(deviceId, longitude, latitude) {
        if (!this.map) {
            console.warn('地图未初始化，无法添加标记');
            return;
        }

        // 创建自定义图标
        const customIcon = L.divIcon({
            html: `
                <div style="
                    width: 24px;
                    height: 24px;
                    background: #667eea;
                    border: 3px solid white;
                    border-radius: 50%;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 12px;
                    font-weight: bold;
                ">📍</div>
            `,
            className: 'custom-marker',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        });

        // 创建标记
        const marker = L.marker([latitude, longitude], { icon: customIcon });

        // 添加弹出窗口
        const popupContent = `
            <div style="padding: 5px; min-width: 200px;">
                <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">🏷️ 设备信息</h4>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>设备ID:</strong> ${deviceId}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>经度:</strong> ${longitude.toFixed(6)}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>纬度:</strong> ${latitude.toFixed(6)}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>时间:</strong> ${new Date().toLocaleString()}</p>
            </div>
        `;

        marker.bindPopup(popupContent);

        // 添加到地图
        marker.addTo(this.map);

        // 存储标记和设备信息
        this.markers.set(deviceId, marker);
        this.deviceList.set(deviceId, {
            id: deviceId,
            longitude,
            latitude,
            timestamp: new Date(),
            status: 'online'
        });

        // 更新统计信息
        this.updateLocationStats();

        // 更新设备列表显示
        this.updateDeviceListDisplay();

        // 自动调整地图视野以包含所有标记
        if (this.markers.size > 0) {
            this.fitMapToMarkers();
        }
    }

    // 更新位置统计信息
    updateLocationStats() {
        const onlineCount = Array.from(this.deviceList.values()).filter(device => device.status === 'online').length;

        if (this.elements.onlineDevices) {
            this.elements.onlineDevices.textContent = onlineCount;
        }

        if (this.elements.totalMarkers) {
            this.elements.totalMarkers.textContent = this.markers.size;
        }

        if (this.elements.lastLocationUpdate) {
            this.elements.lastLocationUpdate.textContent = new Date().toLocaleTimeString();
        }
    }

    // 更新设备列表显示
    updateDeviceListDisplay() {
        if (!this.elements.deviceList) return;

        if (this.deviceList.size === 0) {
            this.elements.deviceList.innerHTML = `
                <div class="no-devices">
                    <i class="fas fa-map-marker-alt"></i>
                    <p>暂无设备位置信息</p>
                    <small>等待接收位置数据...</small>
                </div>
            `;
            return;
        }

        let html = '';
        this.deviceList.forEach((device, deviceId) => {
            html += `
                <div class="device-item" data-device-id="${deviceId}">
                    <div class="device-info">
                        <div class="device-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="device-details">
                            <h4>${deviceId}</h4>
                            <p>位置: ${device.longitude.toFixed(6)}, ${device.latitude.toFixed(6)}</p>
                            <p>更新时间: ${device.timestamp.toLocaleTimeString()}</p>
                        </div>
                    </div>
                    <div class="device-actions">
                        <span class="device-status ${device.status}">${device.status === 'online' ? '在线' : '离线'}</span>
                        <button class="btn btn-outline" onclick="smartRetailSystem.locateDevice('${deviceId}')">
                            <i class="fas fa-crosshairs"></i> 定位
                        </button>
                        <button class="btn btn-outline" onclick="smartRetailSystem.removeDevice('${deviceId}')">
                            <i class="fas fa-trash"></i> 移除
                        </button>
                    </div>
                </div>
            `;
        });

        this.elements.deviceList.innerHTML = html;
    }

    // 定位到指定设备
    locateDevice(deviceId) {
        const marker = this.markers.get(deviceId);
        if (marker && this.map) {
            const latLng = marker.getLatLng();
            this.map.setView(latLng, 15);

            // 打开弹出窗口
            marker.openPopup();

            this.addSystemMessage(`🎯 已定位到设备: ${deviceId}`);
        }
    }

    // 移除设备
    removeDevice(deviceId) {
        const marker = this.markers.get(deviceId);
        if (marker && this.map) {
            this.map.removeLayer(marker);
        }

        this.markers.delete(deviceId);
        this.deviceList.delete(deviceId);

        this.updateLocationStats();
        this.updateDeviceListDisplay();

        this.addSystemMessage(`🗑️ 已移除设备: ${deviceId}`);
    }

    // 调整地图视野以包含所有标记
    fitMapToMarkers() {
        if (!this.map || this.markers.size === 0) return;

        const group = new L.featureGroup(Array.from(this.markers.values()));
        this.map.fitBounds(group.getBounds().pad(0.1));
    }

    // 居中显示地图
    centerMap() {
        if (!this.map) return;

        if (this.markers.size > 0) {
            this.fitMapToMarkers();
        } else {
            this.map.setView([39.90923, 116.397428], 5); // 北京
        }
    }

    // 清除所有标记
    clearAllMarkers() {
        if (!this.map) return;

        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });

        this.markers.clear();
        this.deviceList.clear();

        this.updateLocationStats();
        this.updateDeviceListDisplay();

        this.addSystemMessage('🧹 已清除所有设备标记');
    }

    // 刷新地图
    refreshMap() {
        if (this.map) {
            this.map.destroy();
            this.map = null;
        }

        setTimeout(() => {
            this.initMap();
            this.addSystemMessage('🔄 地图已刷新');
        }, 100);
    }
}

// 页面加载完成后初始化系统
document.addEventListener('DOMContentLoaded', () => {
    window.smartRetailSystem = new SmartRetailSystem();
});

// 后端统一配置文件
const config = {
    // WebSocket服务器配置
    websocket: {
        port: process.env.WS_PORT || 8080, // WebSocket服务器端口
        host: process.env.WS_HOST || 'localhost' // WebSocket服务器地址
    },
    
    // 服务器配置
    server: {
        maxConnections: 100, // 最大连接数
        heartbeatInterval: 30000, // 心跳检测间隔(毫秒)
        messageMaxSize: 1024 * 1024 // 消息最大大小(1MB)
    },
    
    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || 'info', // 日志级别
        enableConsole: true // 是否启用控制台输出
    }
};

module.exports = config;

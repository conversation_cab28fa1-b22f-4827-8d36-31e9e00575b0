<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .input-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { padding: 10px 20px; margin: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        #messages { height: 200px; border: 1px solid #ccc; padding: 10px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接测试</h1>
        
        <div class="input-group">
            <label for="serverAddress">服务器地址:</label>
            <input type="text" id="serverAddress" placeholder="ws://websocket.tlmlab.cn/ 或 localhost:8080" value="localhost:8080">
        </div>
        
        <div>
            <button id="connectBtn">连接</button>
            <button id="disconnectBtn" disabled>断开</button>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="input-group">
            <label for="messageInput">消息:</label>
            <input type="text" id="messageInput" placeholder="输入消息..." disabled>
            <button id="sendBtn" disabled>发送</button>
        </div>
        
        <div id="messages"></div>
    </div>

    <script>
        class WebSocketTest {
            constructor() {
                this.ws = null;
                this.isConnected = false;
                this.initElements();
                this.bindEvents();
            }
            
            initElements() {
                this.elements = {
                    serverAddress: document.getElementById('serverAddress'),
                    connectBtn: document.getElementById('connectBtn'),
                    disconnectBtn: document.getElementById('disconnectBtn'),
                    status: document.getElementById('status'),
                    messageInput: document.getElementById('messageInput'),
                    sendBtn: document.getElementById('sendBtn'),
                    messages: document.getElementById('messages')
                };
            }
            
            bindEvents() {
                this.elements.connectBtn.addEventListener('click', () => this.connect());
                this.elements.disconnectBtn.addEventListener('click', () => this.disconnect());
                this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
                this.elements.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendMessage();
                });
            }
            
            connect() {
                if (this.isConnected) return;
                
                const address = this.elements.serverAddress.value.trim();
                if (!address) {
                    this.addMessage('错误: 请输入服务器地址');
                    return;
                }
                
                let url;
                if (address.startsWith('ws://') || address.startsWith('wss://')) {
                    url = address;
                } else {
                    url = `ws://${address}`;
                }
                
                this.updateStatus('connecting', '连接中...');
                this.addMessage(`正在连接到: ${url}`);
                
                try {
                    this.ws = new WebSocket(url);
                    this.setupEvents();
                } catch (error) {
                    this.updateStatus('disconnected', '连接失败');
                    this.addMessage(`连接失败: ${error.message}`);
                }
            }
            
            setupEvents() {
                this.ws.onopen = () => {
                    this.isConnected = true;
                    this.updateStatus('connected', `已连接到: ${this.elements.serverAddress.value}`);
                    this.addMessage('✅ 连接成功');
                    this.updateUI();
                };
                
                this.ws.onmessage = (event) => {
                    this.addMessage(`收到: ${event.data}`);
                };
                
                this.ws.onclose = () => {
                    this.isConnected = false;
                    this.updateStatus('disconnected', '连接已断开');
                    this.addMessage('❌ 连接已断开');
                    this.updateUI();
                };
                
                this.ws.onerror = (error) => {
                    this.addMessage(`错误: ${error.message || '连接错误'}`);
                };
            }
            
            disconnect() {
                if (this.ws) {
                    this.ws.close();
                }
            }
            
            sendMessage() {
                if (!this.isConnected || !this.ws) return;
                
                const message = this.elements.messageInput.value.trim();
                if (!message) return;
                
                this.ws.send(message);
                this.addMessage(`发送: ${message}`);
                this.elements.messageInput.value = '';
            }
            
            updateStatus(type, text) {
                this.elements.status.className = `status ${type}`;
                this.elements.status.textContent = text;
            }
            
            updateUI() {
                this.elements.connectBtn.disabled = this.isConnected;
                this.elements.disconnectBtn.disabled = !this.isConnected;
                this.elements.messageInput.disabled = !this.isConnected;
                this.elements.sendBtn.disabled = !this.isConnected;
            }
            
            addMessage(text) {
                const div = document.createElement('div');
                div.textContent = `[${new Date().toLocaleTimeString()}] ${text}`;
                this.elements.messages.appendChild(div);
                this.elements.messages.scrollTop = this.elements.messages.scrollHeight;
            }
        }
        
        // 初始化
        new WebSocketTest();
    </script>
</body>
</html>

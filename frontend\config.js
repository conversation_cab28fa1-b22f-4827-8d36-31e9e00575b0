// 前端配置文件
const CONFIG = {
    // 默认WebSocket连接配置
    websocket: {
        defaultHost: 'localhost', // 默认服务器地址
        defaultPort: 8080, // 默认服务器端口
        reconnectInterval: 3000, // 重连间隔(毫秒)
        maxReconnectAttempts: 5 // 最大重连次数
    },
    
    // 界面配置
    ui: {
        maxMessageHistory: 100, // 最大消息历史记录数
        messageDisplayLimit: 50, // 消息显示限制
        autoScroll: true // 自动滚动到最新消息
    },
    
    // 本地存储配置
    storage: {
        serverConfigKey: 'smarttip_server_config', // 服务器配置存储键
        messageHistoryKey: 'smarttip_message_history' // 消息历史存储键
    }
};

// 前端配置文件
const CONFIG = {
    // 应用信息
    app: {
        name: '智能标签管理系统',
        version: '1.0.0',
        description: 'WebSocket实时通信客户端'
    },

    // 默认WebSocket连接配置
    websocket: {
        defaultHost: window.location.hostname || 'localhost', // 默认服务器地址（自动获取当前页面主机名）
        defaultPort: window.location.port || 8080, // 默认服务器端口（自动获取当前页面端口）
        reconnectInterval: 3000, // 重连间隔(毫秒)
        maxReconnectAttempts: 5, // 最大重连次数
        connectionTimeout: 10000, // 连接超时时间(毫秒)
        pingInterval: 25000, // 客户端ping间隔(毫秒)
        protocols: [] // WebSocket子协议
    },

    // 界面配置
    ui: {
        maxMessageHistory: 100, // 最大消息历史记录数
        messageDisplayLimit: 50, // 消息显示限制
        autoScroll: true, // 自动滚动到最新消息
        showTimestamp: true, // 显示消息时间戳
        enableNotifications: false, // 启用浏览器通知
        theme: 'light', // 主题: light, dark
        language: 'zh-CN', // 语言设置
        animationDuration: 300, // 动画持续时间(毫秒)
        messageMaxLength: 1000 // 单条消息最大长度
    },

    // 本地存储配置
    storage: {
        serverConfigKey: 'smarttip_server_config', // 服务器配置存储键
        messageHistoryKey: 'smarttip_message_history', // 消息历史存储键
        userPreferencesKey: 'smarttip_user_preferences', // 用户偏好设置键
        enableLocalStorage: true, // 是否启用本地存储
        storageQuotaLimit: 5 * 1024 * 1024 // 存储配额限制(5MB)
    },

    // 性能配置
    performance: {
        enableVirtualScrolling: false, // 启用虚拟滚动(大量消息时)
        messageRenderBatchSize: 10, // 消息渲染批次大小
        debounceDelay: 300, // 防抖延迟(毫秒)
        throttleDelay: 100 // 节流延迟(毫秒)
    },

    // 安全配置
    security: {
        enableInputSanitization: true, // 启用输入清理
        maxInputLength: 1000, // 最大输入长度
        allowedProtocols: ['ws:', 'wss:'], // 允许的协议
        enableCSP: true // 启用内容安全策略
    },

    // 调试配置
    debug: {
        enableConsoleLog: true, // 启用控制台日志
        enablePerformanceMonitoring: false, // 启用性能监控
        logLevel: 'info', // 日志级别: debug, info, warn, error
        enableErrorReporting: true // 启用错误报告
    },

    // 消息类型配置
    messageTypes: {
        system: {
            color: '#ff9800',
            icon: '🔔',
            prefix: '[系统]'
        },
        sent: {
            color: '#2196f3',
            icon: '📤',
            prefix: '[发送]'
        },
        received: {
            color: '#9c27b0',
            icon: '📥',
            prefix: '[接收]'
        },
        error: {
            color: '#f44336',
            icon: '❌',
            prefix: '[错误]'
        },
        warning: {
            color: '#ff5722',
            icon: '⚠️',
            prefix: '[警告]'
        }
    }
};

// 配置验证函数
CONFIG.validate = function() {
    const errors = [];

    // 验证端口号
    if (this.websocket.defaultPort < 1 || this.websocket.defaultPort > 65535) {
        errors.push(`无效的默认端口号: ${this.websocket.defaultPort}`);
    }

    // 验证重连配置
    if (this.websocket.reconnectInterval < 1000) {
        errors.push('重连间隔不能小于1秒');
    }

    if (this.websocket.maxReconnectAttempts < 0 || this.websocket.maxReconnectAttempts > 20) {
        errors.push('最大重连次数应在0-20之间');
    }

    // 验证UI配置
    if (this.ui.messageDisplayLimit < 10 || this.ui.messageDisplayLimit > 1000) {
        errors.push('消息显示限制应在10-1000之间');
    }

    if (this.ui.messageMaxLength < 1 || this.ui.messageMaxLength > 10000) {
        errors.push('消息最大长度应在1-10000之间');
    }

    if (errors.length > 0) {
        console.error('❌ 前端配置验证失败:', errors);
        return false;
    }

    console.log('✅ 前端配置验证通过');
    return true;
};

// 获取配置摘要
CONFIG.getSummary = function() {
    return {
        app: this.app.name + ' v' + this.app.version,
        defaultServer: `${this.websocket.defaultHost}:${this.websocket.defaultPort}`,
        messageLimit: this.ui.messageDisplayLimit,
        autoReconnect: this.websocket.maxReconnectAttempts > 0,
        localStorage: this.storage.enableLocalStorage
    };
};

// 初始化时验证配置
if (typeof window !== 'undefined') {
    CONFIG.validate();
}

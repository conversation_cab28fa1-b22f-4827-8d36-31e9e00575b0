<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能标签管理系统 - WebSocket客户端</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <header class="header">
            <h1>🏷️ 智能标签管理系统</h1>
            <p class="subtitle">WebSocket实时通信客户端</p>
        </header>

        <!-- 服务器连接配置区域 -->
        <section class="connection-section">
            <h2>🔗 服务器连接配置</h2>
            <div class="connection-form">
                <div class="input-group">
                    <label for="serverHost">服务器地址:</label>
                    <input type="text" id="serverHost" placeholder="localhost" value="localhost">
                </div>
                <div class="input-group">
                    <label for="serverPort">端口号:</label>
                    <input type="number" id="serverPort" placeholder="8080" value="8080" min="1" max="65535">
                </div>
                <div class="button-group">
                    <button id="connectBtn" class="btn btn-primary">连接服务器</button>
                    <button id="disconnectBtn" class="btn btn-secondary" disabled>断开连接</button>
                </div>
            </div>
            
            <!-- 连接状态显示 -->
            <div class="status-display">
                <div class="status-item">
                    <span class="status-label">连接状态:</span>
                    <span id="connectionStatus" class="status-value status-disconnected">未连接</span>
                </div>
                <div class="status-item">
                    <span class="status-label">服务器地址:</span>
                    <span id="serverAddress" class="status-value">-</span>
                </div>
                <div class="status-item">
                    <span class="status-label">客户端ID:</span>
                    <span id="clientId" class="status-value">-</span>
                </div>
            </div>
        </section>

        <!-- 消息交互区域 -->
        <section class="message-section">
            <h2>💬 消息交互</h2>
            
            <!-- 消息显示区域 -->
            <div class="message-display">
                <div id="messageContainer" class="message-container">
                    <div class="welcome-message">
                        <p>👋 欢迎使用智能标签管理系统！</p>
                        <p>请先连接到WebSocket服务器开始使用。</p>
                    </div>
                </div>
            </div>
            
            <!-- 消息输入区域 -->
            <div class="message-input">
                <div class="input-container">
                    <textarea id="messageInput" placeholder="请输入要发送的消息..." rows="3" disabled></textarea>
                    <div class="input-actions">
                        <button id="clearBtn" class="btn btn-outline">清空消息</button>
                        <button id="sendBtn" class="btn btn-primary" disabled>发送消息</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系统信息区域 -->
        <section class="info-section">
            <h2>ℹ️ 系统信息</h2>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">发送消息数:</span>
                    <span id="sentCount" class="info-value">0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">接收消息数:</span>
                    <span id="receivedCount" class="info-value">0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">连接时长:</span>
                    <span id="connectionTime" class="info-value">00:00:00</span>
                </div>
                <div class="info-item">
                    <span class="info-label">最后活动:</span>
                    <span id="lastActivity" class="info-value">-</span>
                </div>
            </div>
        </section>

        <!-- 操作说明 -->
        <section class="help-section">
            <h2>📖 使用说明</h2>
            <div class="help-content">
                <ol>
                    <li><strong>连接服务器</strong>: 输入服务器地址和端口号，点击"连接服务器"按钮</li>
                    <li><strong>发送消息</strong>: 连接成功后，在消息输入框中输入内容，点击"发送消息"</li>
                    <li><strong>接收消息</strong>: 其他客户端发送的消息会自动显示在消息区域</li>
                    <li><strong>断开连接</strong>: 点击"断开连接"按钮或关闭页面</li>
                </ol>
                <div class="tips">
                    <p><strong>💡 提示</strong>: 支持多个客户端同时连接，消息会实时转发给所有其他客户端</p>
                </div>
            </div>
        </section>
    </div>

    <!-- 引入配置文件和脚本文件 -->
    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>

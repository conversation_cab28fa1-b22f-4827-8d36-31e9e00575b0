// 智能零售标签系统 - 前端脚本文件

class SmartRetailSystem {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
        this.reconnectTimer = null;
        this.connectionTimer = null;
        this.connectionStartTime = null;
        this.sentCount = 0;
        this.receivedCount = 0;
        this.connectedAddress = '';
        this.currentPage = 'home';
        this.map = null;
        this.markers = new Map(); // 存储设备标记
        this.deviceList = new Map(); // 存储设备信息
        this.currentDevice = null; // 当前在线设备
        this.deviceTimeout = null; // 设备超时定时器
        this.DEVICE_TIMEOUT = 50000; // 50秒超时

        this.initElements();
        this.bindEvents();
        this.loadConfig();
        this.initNavigation();
    }
    
    // 初始化DOM元素引用
    initElements() {
        console.log('正在初始化DOM元素...');
        this.elements = {
            // 导航元素
            navItems: document.querySelectorAll('.nav-item'),
            pageContents: document.querySelectorAll('.page-content'),
            
            // 连接相关元素
            connectBtn: document.getElementById('connectBtn'),
            disconnectBtn: document.getElementById('disconnectBtn'),
            connectionDot: document.getElementById('connectionDot'),
            connectionText: document.getElementById('connectionText'),
            connectionModal: document.getElementById('connectionModal'),
            modalClose: document.getElementById('modalClose'),
            modalCancel: document.getElementById('modalCancel'),
            modalConnect: document.getElementById('modalConnect'),
            modalServerAddress: document.getElementById('modalServerAddress'),
            
            // 终端页面元素
            serverAddress: document.getElementById('serverAddress'),
            connectionStatus: document.getElementById('connectionStatus'),
            serverAddressDisplay: document.getElementById('serverAddressDisplay'),
            clientId: document.getElementById('clientId'),
            messageContainer: document.getElementById('messageContainer'),
            messageInput: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            clearBtn: document.getElementById('clearBtn'),
            sentCount: document.getElementById('sentCount'),
            receivedCount: document.getElementById('receivedCount'),
            connectionTime: document.getElementById('connectionTime'),
            lastActivity: document.getElementById('lastActivity'),

            // 定位界面元素
            mapContainer: document.getElementById('mapContainer'),
            centerMapBtn: document.getElementById('centerMapBtn'),
            clearMarkersBtn: document.getElementById('clearMarkersBtn'),
            refreshMapBtn: document.getElementById('refreshMapBtn'),
            onlineDevices: document.getElementById('onlineDevices'),
            totalMarkers: document.getElementById('totalMarkers'),
            lastLocationUpdate: document.getElementById('lastLocationUpdate'),
            deviceList: document.getElementById('deviceList')
        };

        console.log('DOM元素初始化完成:', {
            navItems: this.elements.navItems.length,
            connectBtn: !!this.elements.connectBtn,
            pageContents: this.elements.pageContents.length
        });
    }
    
    // 绑定事件监听器
    bindEvents() {
        // 导航事件
        this.elements.navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const page = e.currentTarget.dataset.page;
                this.switchPage(page);
            });
        });
        
        // 连接按钮事件
        if (this.elements.connectBtn) {
            this.elements.connectBtn.addEventListener('click', () => this.showConnectionModal());
        }
        
        if (this.elements.disconnectBtn) {
            this.elements.disconnectBtn.addEventListener('click', () => this.disconnect());
        }
        
        // 模态框事件
        if (this.elements.modalClose) {
            this.elements.modalClose.addEventListener('click', () => this.hideConnectionModal());
        }
        
        if (this.elements.modalCancel) {
            this.elements.modalCancel.addEventListener('click', () => this.hideConnectionModal());
        }
        
        if (this.elements.modalConnect) {
            this.elements.modalConnect.addEventListener('click', () => this.connectFromModal());
        }
        
        // 消息发送事件
        if (this.elements.sendBtn) {
            this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
        }
        
        if (this.elements.messageInput) {
            this.elements.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }
        
        // 清空消息事件
        if (this.elements.clearBtn) {
            this.elements.clearBtn.addEventListener('click', () => this.clearMessages());
        }
        
        // 模态框外部点击关闭
        if (this.elements.connectionModal) {
            this.elements.connectionModal.addEventListener('click', (e) => {
                if (e.target === this.elements.connectionModal) {
                    this.hideConnectionModal();
                }
            });
        }

        // 定位界面事件
        if (this.elements.centerMapBtn) {
            this.elements.centerMapBtn.addEventListener('click', () => this.centerMap());
        }

        if (this.elements.clearMarkersBtn) {
            this.elements.clearMarkersBtn.addEventListener('click', () => this.clearAllMarkers());
        }

        if (this.elements.refreshMapBtn) {
            this.elements.refreshMapBtn.addEventListener('click', () => this.refreshMap());
        }
    }
    
    // 初始化导航
    initNavigation() {
        this.switchPage('home');
    }
    
    // 切换页面
    switchPage(page) {
        // 更新导航状态
        this.elements.navItems.forEach(item => {
            item.classList.remove('active');
            if (item.dataset.page === page) {
                item.classList.add('active');
            }
        });
        
        // 更新页面内容
        this.elements.pageContents.forEach(content => {
            content.classList.remove('active');
            if (content.id === `${page}-page`) {
                content.classList.add('active');
            }
        });
        
        this.currentPage = page;

        // 如果切换到定位页面，初始化地图
        if (page === 'location' && !this.map) {
            setTimeout(() => this.initMap(), 100);
        }
    }
    
    // 显示连接模态框
    showConnectionModal() {
        if (this.elements.connectionModal) {
            this.elements.connectionModal.classList.add('active');
            if (this.elements.modalServerAddress) {
                this.elements.modalServerAddress.focus();
            }
        }
    }
    
    // 隐藏连接模态框
    hideConnectionModal() {
        if (this.elements.connectionModal) {
            this.elements.connectionModal.classList.remove('active');
        }
    }
    
    // 从模态框连接
    connectFromModal() {
        const address = this.elements.modalServerAddress?.value.trim();
        if (address) {
            this.connect(address);
            this.hideConnectionModal();
        }
    }
    
    // 连接WebSocket服务器
    connect(address = null) {
        if (this.isConnected) return;
        
        const serverAddress = address || this.elements.serverAddress?.value.trim() || 'localhost:8082';
        
        // 构建WebSocket URL
        let url;
        if (serverAddress.startsWith('ws://') || serverAddress.startsWith('wss://')) {
            url = serverAddress;
        } else {
            url = `ws://${serverAddress}`;
        }
        
        this.connectedAddress = serverAddress;
        this.updateConnectionStatus('connecting', '连接中...');
        this.addSystemMessage(`正在连接到 ${url}...`);
        
        try {
            this.ws = new WebSocket(url);
            this.setupWebSocketEvents();
        } catch (error) {
            this.handleConnectionError('连接失败: ' + error.message);
        }
    }
    
    // 设置WebSocket事件监听
    setupWebSocketEvents() {
        this.ws.onopen = () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.connectionStartTime = new Date();
            this.startConnectionTimer();
            
            console.log('WebSocket连接成功:', this.connectedAddress);
            console.log('WebSocket URL:', this.ws.url);
            this.updateConnectionStatus('connected', '已连接');
            if (this.elements.serverAddressDisplay) {
                this.elements.serverAddressDisplay.textContent = this.connectedAddress;
            }
            this.addSystemMessage(`✅ 成功连接到服务器: ${this.connectedAddress}`);
            this.addSystemMessage(`🔗 实际连接URL: ${this.ws.url}`);
            this.updateUI();
        };
        
        this.ws.onmessage = (event) => {
            console.log('收到WebSocket消息:', event.data);
            this.handleMessage(event.data);
            this.updateLastActivity();
        };
        
        this.ws.onclose = (event) => {
            this.isConnected = false;
            this.clearConnectionTimer();
            this.updateConnectionStatus('disconnected', '连接已断开');
            this.addSystemMessage('❌ 连接已断开');
            this.updateUI();
            
            if (this.reconnectAttempts < this.maxReconnectAttempts && !event.wasClean) {
                this.scheduleReconnect();
            }
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.addSystemMessage('❌ 连接错误');
        };
    }
    
    // 断开连接
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, '用户主动断开');
            this.clearReconnectTimer();
        }
    }
    
    // 处理接收到的消息
    handleMessage(data) {
        this.receivedCount++;
        if (this.elements.receivedCount) {
            this.elements.receivedCount.textContent = this.receivedCount;
        }

        // 检查是否为位置信息格式：经度_纬度
        if (this.isLocationData(data)) {
            this.handleLocationMessage(data);
            return;
        }

        try {
            const message = JSON.parse(data);
            if (message.type === 'welcome') {
                this.clientId = message.clientId;
                if (this.elements.clientId) {
                    this.elements.clientId.textContent = this.clientId;
                }
                this.addSystemMessage(`🆔 客户端ID: ${this.clientId}`);
            } else {
                this.addReceivedMessage(data);
            }
        } catch (error) {
            this.addReceivedMessage(data);
        }
    }
    
    // 发送消息
    sendMessage() {
        if (!this.isConnected || !this.ws) {
            this.addSystemMessage('❌ 请先连接到服务器');
            return;
        }
        
        const message = this.elements.messageInput?.value.trim();
        if (!message) return;
        
        try {
            console.log('发送消息:', message, '到服务器:', this.connectedAddress);
            console.log('WebSocket状态:', this.ws.readyState);
            this.ws.send(message);
            this.sentCount++;
            if (this.elements.sentCount) {
                this.elements.sentCount.textContent = this.sentCount;
            }
            this.addSentMessage(message);
            if (this.elements.messageInput) {
                this.elements.messageInput.value = '';
            }
            this.updateLastActivity();
            console.log('消息发送成功');
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addSystemMessage('❌ 发送消息失败: ' + error.message);
        }
    }
    
    // 更新连接状态
    updateConnectionStatus(status, text) {
        if (this.elements.connectionDot) {
            this.elements.connectionDot.className = 'status-dot';
            if (status === 'connected') {
                this.elements.connectionDot.classList.add('connected');
            }
        }
        
        if (this.elements.connectionText) {
            this.elements.connectionText.textContent = text;
        }
        
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.className = `status-value status-${status}`;
            this.elements.connectionStatus.textContent = text;
        }
    }
    
    // 更新界面状态
    updateUI() {
        if (this.elements.connectBtn) {
            this.elements.connectBtn.textContent = this.isConnected ? '已连接' : '连接系统';
            this.elements.connectBtn.disabled = this.isConnected;
        }

        if (this.elements.disconnectBtn) {
            this.elements.disconnectBtn.disabled = !this.isConnected;
        }

        if (this.elements.messageInput) {
            this.elements.messageInput.disabled = !this.isConnected;
        }

        if (this.elements.sendBtn) {
            this.elements.sendBtn.disabled = !this.isConnected;
        }

        if (!this.isConnected) {
            if (this.elements.serverAddressDisplay) {
                this.elements.serverAddressDisplay.textContent = '-';
            }
            if (this.elements.clientId) {
                this.elements.clientId.textContent = '-';
            }
        }
    }

    // 添加系统消息
    addSystemMessage(message) {
        this.addMessage(message, 'system');
    }

    // 添加发送的消息
    addSentMessage(message) {
        this.addMessage(`我: ${message}`, 'sent');
    }

    // 添加接收的消息
    addReceivedMessage(message) {
        this.addMessage(`收到: ${message}`, 'received');
    }

    // 添加消息到容器
    addMessage(content, type) {
        if (!this.elements.messageContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message-item message-${type}`;

        const contentDiv = document.createElement('div');
        contentDiv.textContent = content;
        messageDiv.appendChild(contentDiv);

        const timestampDiv = document.createElement('div');
        timestampDiv.className = 'message-timestamp';
        timestampDiv.textContent = new Date().toLocaleTimeString();
        messageDiv.appendChild(timestampDiv);

        this.elements.messageContainer.appendChild(messageDiv);
        this.elements.messageContainer.scrollTop = this.elements.messageContainer.scrollHeight;
    }

    // 清空消息
    clearMessages() {
        if (this.elements.messageContainer) {
            this.elements.messageContainer.innerHTML = `
                <div class="welcome-message">
                    <p>👋 欢迎使用智能标签管理系统终端！</p>
                    <p>请先连接到WebSocket服务器开始使用。</p>
                </div>
            `;
        }
    }

    // 开始连接时长计时
    startConnectionTimer() {
        this.connectionTimer = setInterval(() => {
            if (this.connectionStartTime && this.elements.connectionTime) {
                const duration = Date.now() - this.connectionStartTime.getTime();
                this.elements.connectionTime.textContent = this.formatDuration(duration);
            }
        }, 1000);
    }

    // 清除连接计时器
    clearConnectionTimer() {
        if (this.connectionTimer) {
            clearInterval(this.connectionTimer);
            this.connectionTimer = null;
        }
    }

    // 更新最后活动时间
    updateLastActivity() {
        if (this.elements.lastActivity) {
            this.elements.lastActivity.textContent = new Date().toLocaleTimeString();
        }
    }

    // 格式化持续时间
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        const h = hours.toString().padStart(2, '0');
        const m = (minutes % 60).toString().padStart(2, '0');
        const s = (seconds % 60).toString().padStart(2, '0');

        return `${h}:${m}:${s}`;
    }

    // 处理连接错误
    handleConnectionError(message) {
        this.updateConnectionStatus('disconnected', '连接失败');
        this.addSystemMessage(`❌ ${message}`);
        this.updateUI();
    }

    // 安排重连
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectInterval * this.reconnectAttempts;

        this.addSystemMessage(`🔄 ${delay/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

        this.reconnectTimer = setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }

    // 清除重连定时器
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        this.reconnectAttempts = 0;
    }

    // 加载配置
    loadConfig() {
        try {
            const savedAddress = localStorage.getItem('smartRetail_serverAddress');
            if (savedAddress) {
                if (this.elements.serverAddress) {
                    this.elements.serverAddress.value = savedAddress;
                }
                if (this.elements.modalServerAddress) {
                    this.elements.modalServerAddress.value = savedAddress;
                }
            }
        } catch (error) {
            console.warn('加载配置失败:', error);
        }
    }

    // 保存配置
    saveConfig() {
        try {
            const address = this.elements.serverAddress?.value || this.elements.modalServerAddress?.value;
            if (address) {
                localStorage.setItem('smartRetail_serverAddress', address);
            }
        } catch (error) {
            console.warn('保存配置失败:', error);
        }
    }

    // 检查是否为位置数据格式
    isLocationData(data) {
        const locationPattern = /^(\d+\.\d+)_(\d+\.\d+)$/;
        return locationPattern.test(data.trim());
    }

    // 处理位置消息
    handleLocationMessage(data) {
        const [longitude, latitude] = data.trim().split('_').map(Number);

        if (this.isValidCoordinate(longitude, latitude)) {
            // 中枢设备ID固定为 "central_device"
            const deviceId = "central_device";
            this.updateDeviceLocation(deviceId, longitude, latitude);
            this.addSystemMessage(`📍 收到中枢设备位置: ${longitude}, ${latitude}`);
        } else {
            this.addSystemMessage(`❌ 无效的位置信息: ${data}`);
        }
    }

    // 验证坐标有效性
    isValidCoordinate(lng, lat) {
        // 扩大坐标范围以包含更多区域：经度70-140，纬度15-60
        return lng >= 70 && lng <= 140 && lat >= 15 && lat <= 60;
    }

    // 初始化地图
    initMap() {
        if (!window.L) {
            this.showMapError('地图API加载失败，请检查网络连接');
            return;
        }

        try {
            // 创建地图实例，优化性能设置
            this.map = L.map('mapContainer', {
                preferCanvas: true, // 使用Canvas渲染提高性能
                zoomControl: true,
                attributionControl: true
            }).setView([35.0, 105.0], 4); // 中国中心位置

            // 添加瓦片图层 - 使用更快的瓦片服务
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18,
                minZoom: 3,
                updateWhenIdle: true, // 优化性能
                keepBuffer: 2 // 减少缓存
            }).addTo(this.map);

            // 添加比例尺控件
            L.control.scale({
                position: 'bottomright',
                imperial: false
            }).addTo(this.map);

            // 隐藏加载提示
            const loadingEl = this.elements.mapContainer?.querySelector('.map-loading');
            if (loadingEl) {
                loadingEl.style.display = 'none';
            }

            console.log('地图初始化成功');
        } catch (error) {
            console.error('地图初始化失败:', error);
            this.showMapError('地图初始化失败: ' + error.message);
        }
    }

    // 显示地图错误
    showMapError(message) {
        if (this.elements.mapContainer) {
            this.elements.mapContainer.innerHTML = `
                <div class="map-loading">
                    <i class="fas fa-exclamation-triangle" style="color: #e74c3c;"></i>
                    <p style="color: #e74c3c;">${message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }

    // 更新设备位置
    updateDeviceLocation(deviceId, longitude, latitude) {
        if (!this.map) {
            console.warn('地图未初始化，无法添加标记');
            return;
        }

        const timestamp = new Date();

        // 创建历史标记（灰色小点）
        const historyIcon = L.divIcon({
            html: `
                <div style="
                    width: 8px;
                    height: 8px;
                    background: #95a5a6;
                    border: 2px solid white;
                    border-radius: 50%;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                "></div>
            `,
            className: 'history-marker',
            iconSize: [12, 12],
            iconAnchor: [6, 6]
        });

        // 添加历史标记
        const historyMarkerId = `history_${timestamp.getTime()}`;
        const historyMarker = L.marker([latitude, longitude], { icon: historyIcon });
        historyMarker.bindPopup(`
            <div style="padding: 5px; min-width: 150px;">
                <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 12px;">📍 历史位置</h4>
                <p style="margin: 2px 0; color: #6c757d; font-size: 11px;"><strong>坐标:</strong> ${longitude.toFixed(6)}, ${latitude.toFixed(6)}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 11px;"><strong>时间:</strong> ${timestamp.toLocaleString()}</p>
            </div>
        `);
        historyMarker.addTo(this.map);
        this.markers.set(historyMarkerId, historyMarker);

        // 更新或创建当前设备标记
        this.updateCurrentDeviceMarker(deviceId, longitude, latitude, timestamp);

        // 重置设备超时定时器
        this.resetDeviceTimeout(deviceId);

        // 更新统计信息
        this.updateLocationStats();

        // 更新设备列表显示
        this.updateDeviceListDisplay();

        // 自动调整地图视野以包含所有标记
        if (this.markers.size > 0) {
            this.fitMapToMarkers();
        }
    }

    // 更新当前设备标记
    updateCurrentDeviceMarker(deviceId, longitude, latitude, timestamp) {
        // 移除旧的当前设备标记
        if (this.currentDevice && this.markers.has(this.currentDevice.markerId)) {
            const oldMarker = this.markers.get(this.currentDevice.markerId);
            this.map.removeLayer(oldMarker);
            this.markers.delete(this.currentDevice.markerId);
        }

        // 创建新的当前设备标记（绿色大点）
        const currentIcon = L.divIcon({
            html: `
                <div style="
                    width: 20px;
                    height: 20px;
                    background: #27ae60;
                    border: 3px solid white;
                    border-radius: 50%;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.4);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                    animation: pulse 2s infinite;
                ">🏢</div>
            `,
            className: 'current-device-marker',
            iconSize: [26, 26],
            iconAnchor: [13, 13]
        });

        const currentMarkerId = `current_${deviceId}`;
        const currentMarker = L.marker([latitude, longitude], { icon: currentIcon });
        currentMarker.bindPopup(`
            <div style="padding: 8px; min-width: 200px;">
                <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">🏢 中枢设备</h4>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>设备ID:</strong> ${deviceId}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>经度:</strong> ${longitude.toFixed(6)}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>纬度:</strong> ${latitude.toFixed(6)}</p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>状态:</strong> <span style="color: #27ae60;">在线</span></p>
                <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>最后更新:</strong> ${timestamp.toLocaleString()}</p>
            </div>
        `);
        currentMarker.addTo(this.map);
        this.markers.set(currentMarkerId, currentMarker);

        // 更新当前设备信息
        this.currentDevice = {
            id: deviceId,
            markerId: currentMarkerId,
            longitude,
            latitude,
            timestamp,
            status: 'online'
        };

        // 更新设备列表中的信息
        this.deviceList.set(deviceId, this.currentDevice);
    }

    // 重置设备超时定时器
    resetDeviceTimeout(deviceId) {
        // 清除旧的定时器
        if (this.deviceTimeout) {
            clearTimeout(this.deviceTimeout);
        }

        // 设置新的定时器
        this.deviceTimeout = setTimeout(() => {
            this.setDeviceOffline(deviceId);
        }, this.DEVICE_TIMEOUT);
    }

    // 设置设备离线
    setDeviceOffline(deviceId) {
        if (this.currentDevice && this.currentDevice.id === deviceId) {
            // 更新设备状态为离线
            this.currentDevice.status = 'offline';
            this.deviceList.set(deviceId, this.currentDevice);

            // 更新当前设备标记为离线状态（红色）
            if (this.markers.has(this.currentDevice.markerId)) {
                const marker = this.markers.get(this.currentDevice.markerId);
                this.map.removeLayer(marker);

                // 创建离线设备标记
                const offlineIcon = L.divIcon({
                    html: `
                        <div style="
                            width: 20px;
                            height: 20px;
                            background: #e74c3c;
                            border: 3px solid white;
                            border-radius: 50%;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.4);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 10px;
                            font-weight: bold;
                        ">❌</div>
                    `,
                    className: 'offline-device-marker',
                    iconSize: [26, 26],
                    iconAnchor: [13, 13]
                });

                const offlineMarker = L.marker([this.currentDevice.latitude, this.currentDevice.longitude], { icon: offlineIcon });
                offlineMarker.bindPopup(`
                    <div style="padding: 8px; min-width: 200px;">
                        <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">🏢 中枢设备</h4>
                        <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>设备ID:</strong> ${deviceId}</p>
                        <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>状态:</strong> <span style="color: #e74c3c;">离线</span></p>
                        <p style="margin: 2px 0; color: #6c757d; font-size: 12px;"><strong>最后在线:</strong> ${this.currentDevice.timestamp.toLocaleString()}</p>
                    </div>
                `);
                offlineMarker.addTo(this.map);
                this.markers.set(this.currentDevice.markerId, offlineMarker);
            }

            this.addSystemMessage(`⚠️ 中枢设备已离线（超过50秒未收到位置信息）`);
            this.updateLocationStats();
            this.updateDeviceListDisplay();
        }
    }

    // 更新位置统计信息
    updateLocationStats() {
        // 在线设备数：只有当前设备在线时为1，否则为0
        const onlineCount = this.currentDevice && this.currentDevice.status === 'online' ? 1 : 0;

        if (this.elements.onlineDevices) {
            this.elements.onlineDevices.textContent = onlineCount;
        }

        if (this.elements.totalMarkers) {
            this.elements.totalMarkers.textContent = this.markers.size;
        }

        if (this.elements.lastLocationUpdate) {
            this.elements.lastLocationUpdate.textContent = new Date().toLocaleTimeString();
        }
    }

    // 更新设备列表显示
    updateDeviceListDisplay() {
        if (!this.elements.deviceList) return;

        if (!this.currentDevice) {
            this.elements.deviceList.innerHTML = `
                <div class="no-devices">
                    <i class="fas fa-microchip"></i>
                    <p>暂无中枢设备信息</p>
                    <small>等待接收位置数据...</small>
                </div>
            `;
            return;
        }

        const device = this.currentDevice;
        const statusText = device.status === 'online' ? '在线' : '离线';
        const statusColor = device.status === 'online' ? '#27ae60' : '#e74c3c';
        const lastUpdate = device.status === 'online' ?
            `最后更新: ${device.timestamp.toLocaleTimeString()}` :
            `最后在线: ${device.timestamp.toLocaleTimeString()}`;

        this.elements.deviceList.innerHTML = `
            <div class="device-item" data-device-id="${device.id}">
                <div class="device-info">
                    <div class="device-icon" style="background: ${statusColor};">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="device-details">
                        <h4>🏢 中枢设备</h4>
                        <p>位置: ${device.longitude.toFixed(6)}, ${device.latitude.toFixed(6)}</p>
                        <p>${lastUpdate}</p>
                    </div>
                </div>
                <div class="device-actions">
                    <span class="device-status ${device.status}">${statusText}</span>
                    <button class="btn btn-outline" onclick="smartRetailSystem.locateDevice('${device.id}')">
                        <i class="fas fa-crosshairs"></i> 定位
                    </button>
                    <button class="btn btn-outline" onclick="smartRetailSystem.clearDeviceHistory()">
                        <i class="fas fa-history"></i> 清除轨迹
                    </button>
                </div>
            </div>
        `;
    }

    // 定位到指定设备
    locateDevice(deviceId) {
        if (this.currentDevice && this.currentDevice.id === deviceId) {
            const marker = this.markers.get(this.currentDevice.markerId);
            if (marker && this.map) {
                const latLng = marker.getLatLng();
                this.map.setView(latLng, 15);

                // 打开弹出窗口
                marker.openPopup();

                this.addSystemMessage(`🎯 已定位到中枢设备`);
            }
        }
    }

    // 清除设备历史轨迹
    clearDeviceHistory() {
        if (!this.map) return;

        // 保留当前设备标记，移除所有历史标记
        const currentMarkerId = this.currentDevice ? this.currentDevice.markerId : null;

        this.markers.forEach((marker, markerId) => {
            if (markerId !== currentMarkerId) {
                this.map.removeLayer(marker);
            }
        });

        // 清除历史标记记录
        const newMarkers = new Map();
        if (currentMarkerId && this.markers.has(currentMarkerId)) {
            newMarkers.set(currentMarkerId, this.markers.get(currentMarkerId));
        }
        this.markers = newMarkers;

        this.updateLocationStats();
        this.addSystemMessage('🧹 已清除历史轨迹，保留当前设备位置');
    }

    // 调整地图视野以包含所有标记
    fitMapToMarkers() {
        if (!this.map || this.markers.size === 0) return;

        const group = new L.featureGroup(Array.from(this.markers.values()));
        this.map.fitBounds(group.getBounds().pad(0.1));
    }

    // 居中显示地图
    centerMap() {
        if (!this.map) return;

        if (this.currentDevice) {
            // 如果有当前设备，直接定位到设备位置
            this.map.setView([this.currentDevice.latitude, this.currentDevice.longitude], 10);
        } else if (this.markers.size > 0) {
            this.fitMapToMarkers();
        } else {
            this.map.setView([35.0, 105.0], 4); // 中国中心
        }
    }

    // 清除所有标记
    clearAllMarkers() {
        if (!this.map) return;

        // 清除设备超时定时器
        if (this.deviceTimeout) {
            clearTimeout(this.deviceTimeout);
            this.deviceTimeout = null;
        }

        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });

        this.markers.clear();
        this.deviceList.clear();
        this.currentDevice = null;

        this.updateLocationStats();
        this.updateDeviceListDisplay();

        this.addSystemMessage('🧹 已清除所有标记和设备信息');
    }

    // 刷新地图
    refreshMap() {
        if (this.map) {
            this.map.remove();
            this.map = null;
        }

        // 清除所有标记和设备信息
        this.markers.clear();
        this.deviceList.clear();
        this.currentDevice = null;

        // 清除设备超时定时器
        if (this.deviceTimeout) {
            clearTimeout(this.deviceTimeout);
            this.deviceTimeout = null;
        }

        setTimeout(() => {
            this.initMap();
            this.updateLocationStats();
            this.updateDeviceListDisplay();
            this.addSystemMessage('🔄 地图已刷新');
        }, 100);
    }
}

// 页面加载完成后初始化系统
document.addEventListener('DOMContentLoaded', () => {
    window.smartRetailSystem = new SmartRetailSystem();
});

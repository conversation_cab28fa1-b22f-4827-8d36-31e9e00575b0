// WebSocket连接测试工具
const WebSocket = require('ws');

function testWebSocketConnection(url) {
    console.log(`🔍 测试WebSocket连接: ${url}`);
    console.log('=' .repeat(50));
    
    const startTime = Date.now();
    
    try {
        const ws = new WebSocket(url, {
            timeout: 10000, // 10秒超时
            headers: {
                'User-Agent': 'SmartTip-WebSocket-Test/1.0'
            }
        });
        
        ws.on('open', function open() {
            const duration = Date.now() - startTime;
            console.log(`✅ 连接成功! 耗时: ${duration}ms`);
            console.log(`📡 连接状态: ${ws.readyState}`);
            console.log(`🌐 URL: ${ws.url}`);
            
            // 发送测试消息
            ws.send('Hello from SmartTip test client');
            console.log('📤 已发送测试消息');
            
            // 5秒后关闭连接
            setTimeout(() => {
                ws.close();
                console.log('🔌 连接已关闭');
            }, 5000);
        });
        
        ws.on('message', function message(data) {
            console.log(`📥 收到消息: ${data}`);
        });
        
        ws.on('close', function close(code, reason) {
            const duration = Date.now() - startTime;
            console.log(`🔌 连接关闭 - 代码: ${code}, 原因: ${reason || '无'}, 总耗时: ${duration}ms`);
        });
        
        ws.on('error', function error(err) {
            const duration = Date.now() - startTime;
            console.log(`❌ 连接错误 (${duration}ms):`);
            console.log(`   错误类型: ${err.name}`);
            console.log(`   错误消息: ${err.message}`);
            console.log(`   错误代码: ${err.code || '无'}`);
            
            if (err.code) {
                switch (err.code) {
                    case 'ENOTFOUND':
                        console.log('   💡 建议: 检查域名是否正确');
                        break;
                    case 'ECONNREFUSED':
                        console.log('   💡 建议: 服务器拒绝连接，检查端口是否开放');
                        break;
                    case 'ETIMEDOUT':
                        console.log('   💡 建议: 连接超时，检查网络或防火墙');
                        break;
                    case 'ECONNRESET':
                        console.log('   💡 建议: 连接被重置，可能是服务器问题');
                        break;
                    default:
                        console.log(`   💡 建议: 未知错误，请检查网络连接`);
                }
            }
        });
        
        // 超时处理
        setTimeout(() => {
            if (ws.readyState === WebSocket.CONNECTING) {
                console.log('⏰ 连接超时，正在关闭...');
                ws.terminate();
            }
        }, 15000);
        
    } catch (error) {
        console.log(`❌ 创建WebSocket时发生错误:`);
        console.log(`   ${error.message}`);
    }
}

// 测试多个可能的WebSocket地址
const testUrls = [
    'ws://websocket.tlmlab.cn/',
    'ws://websocket.tlmlab.cn:80/',
    'ws://websocket.tlmlab.cn:8080/',
    'wss://websocket.tlmlab.cn/',
    'wss://websocket.tlmlab.cn:443/'
];

console.log('🚀 开始WebSocket连接测试');
console.log(`📅 测试时间: ${new Date().toLocaleString()}`);
console.log('');

// 逐个测试每个URL
let currentTest = 0;

function runNextTest() {
    if (currentTest >= testUrls.length) {
        console.log('\n🏁 所有测试完成');
        console.log('');
        console.log('📋 可能的解决方案:');
        console.log('1. 确认WebSocket服务器是否正在运行');
        console.log('2. 检查服务器是否支持WebSocket协议');
        console.log('3. 尝试使用不同的端口 (80, 443, 8080, 8443)');
        console.log('4. 检查是否需要特定的路径或参数');
        console.log('5. 确认服务器是否有CORS或其他安全限制');
        return;
    }
    
    const url = testUrls[currentTest];
    console.log(`\n📍 测试 ${currentTest + 1}/${testUrls.length}: ${url}`);
    
    testWebSocketConnection(url);
    
    // 等待20秒后测试下一个
    setTimeout(() => {
        currentTest++;
        runNextTest();
    }, 20000);
}

// 开始测试
runNextTest();

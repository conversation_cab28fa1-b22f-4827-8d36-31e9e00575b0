# 🏷️ 智能零售标签系统

> 基于CH32V307芯片的新一代零售价格管理解决方案，采用WebSocket实时通信技术

[![Node.js](https://img.shields.io/badge/Node.js-14%2B-green.svg)](https://nodejs.org/)
[![WebSocket](https://img.shields.io/badge/WebSocket-RFC6455-blue.svg)](https://tools.ietf.org/html/rfc6455)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📋 项目概述

智能零售标签系统面向传统零售场景中价格调整滞后、促销推送效率低等痛点，设计并实现了一套基于CH32V307芯片的智能零售标签系统。系统由中枢系统与终端设备两部分组成，通过Zigbee无线组网，实现对终端墨水屏标签的远程批量管理与价格精准调控。

### 🎯 系统特点

- **中枢系统**: 基于CH32V307芯片，内置GD25Q40 Flash文件系统与Shell操作环境
- **终端设备**: 低功耗墨水屏标签，长期稳定显示价格及促销信息
- **AI智能调控**: 根据市场行情、库存水平及促销策略自动优化价格
- **云端管理**: 网页界面远程监控，实时查看设备状态和定位信息

### ✨ 核心特性

- ⚡ **高效价格管理** - 解决传统零售场景中价格调整滞后、促销推送效率低等痛点
- 📡 **Zigbee无线组网** - 通过Zigbee技术实现终端墨水屏标签的远程批量管理
- 👁️ **实时状态监控** - 本地屏幕实时展示系统状态，网页界面远程监控网络运行
- 🧠 **AI智能定价** - 集成AI算法，根据市场行情自动优化价格，实现动态化定价
- 🍃 **节能环保** - 墨水屏技术，超低功耗，长期稳定显示，绿色环保
- 🔧 **可扩展架构** - 模块化设计，具备良好的可扩展性和易维护性
- 🎨 **现代化界面** - 5个专业界面：首页、终端、显示调整、定位、AI调控
- 🔄 **实时通信** - 基于WebSocket的高性能实时通信系统
- 📱 **响应式设计** - 支持桌面端和移动端访问

### 🏗️ 系统架构

```
┌─────────────────┐                  ┌─────────────────┐
│    云端服务     │                  │   网页管理平台   │
│                │                  │                │
│ • 数据分析      │ ◄──────────────► │ • 5个专业界面   │
│ • AI算法       │                  │ • WebSocket通信 │
└─────────────────┘                  │ • 响应式设计    │
         ▲                           └─────────────────┘
         │                                    ▲
         ▼                                    │
┌─────────────────┐    Zigbee组网     ┌─────────────────┐
│    中枢系统     │ ◄──────────────► │   终端设备群    │
│                │                  │                │
│ • CH32V307芯片  │                  │ • 墨水屏标签    │
│ • GD25Q40 Flash│                  │ • 低功耗设计    │
│ • Shell环境    │                  │ • 价格显示      │
└─────────────────┘                  └─────────────────┘
```

### 🖥️ 界面功能

系统提供5个专业管理界面：

#### 🏠 首页界面
- **系统概览**: 展示系统核心特性和架构图
- **功能卡片**: 中枢系统、终端设备、AI调控、云端管理
- **特性介绍**: 高效价格管理、Zigbee组网、实时监控等
- **架构展示**: 可视化系统架构和数据流向

#### 💻 终端页面
- **WebSocket通信**: 实时消息交互终端
- **连接管理**: 服务器连接配置和状态监控
- **消息收发**: 支持实时消息发送和接收
- **统计信息**: 连接时长、消息计数、活动状态

#### 🎛️ 显示调整界面
- **动态设备管理**: 根据`device_number:数量`格式自动生成设备列表
- **商品信息配置**: 支持商品名称、厂地、规格、条码、零售价、会员价设置
- **智能搜索筛选**: 支持按商品名称、条码、厂地搜索和状态筛选
- **批量操作**: 一键更新所有已保存设备，支持数据导出
- **实时状态管理**: 设备状态实时更新（待更新/已保存/已更新）

##### 功能特点
- **自动设备生成**: 接收`device_number:100`格式消息自动生成100个设备
- **中国地级市支持**: 内置70+个主要城市作为厂地选项
- **分页显示**: 每页12个设备，支持分页浏览
- **JSON格式下发**: 更新时以标准JSON格式发送设备信息
- **状态追踪**: 区分保存和更新操作，支持批量处理

##### 使用流程
1. **接收设备数量**: 通过WebSocket接收`device_number:数量`消息
2. **配置商品信息**: 填写商品名称、选择厂地、输入价格等
3. **保存设备**: 点击"保存"按钮保存设备信息（不立即更新）
4. **单独更新**: 点击"更新"按钮立即发送JSON格式更新指令
5. **批量更新**: 点击"一键更新"同时更新所有已保存的设备

##### 数据格式
更新指令JSON格式：
```json
{
  "deviceId": "device_001",
  "productInfo": {
    "productName": "商品名称",
    "manufacturer": "广东东莞",
    "specifications": "规格信息",
    "barcode": "条码",
    "retailPrice": 10.50,
    "memberPrice": 9.50
  },
  "timestamp": "2025-01-01T12:00:00.000Z"
}
```

##### 设备数量消息格式
系统接收以下格式的设备数量信息：
```
device_number:数量
例如：device_number:50
```
- 支持1-9999个设备
- 自动生成设备ID（device_001, device_002...）
- 实时更新设备统计信息

#### 📍 定位界面
- **实时地图**: 基于Leaflet的交互式中国地图显示
- **位置解析**: 自动解析"经度_纬度"格式的位置信息
- **设备标记**: 在地图上实时显示设备位置标记
- **设备管理**: 设备列表、定位、移除等操作
- **统计信息**: 在线设备数、总标记数、最后更新时间
- **地图控制**: 居中显示、清除标记、刷新地图等功能

##### 位置数据格式
系统接收以下格式的位置信息：
```
经度_纬度
例如：116.397428_39.90923
```
- 经度范围：73-135（中国境内）
- 纬度范围：18-54（中国境内）
- 分隔符：下划线(_)

#### 🤖 AI调控界面
- **GLM-4集成**: 集成智谱AI GLM-4系列模型
- **智能对话**: 专业的零售价格调控AI助手
- **策略分析**: AI驱动的定价策略和市场分析
- **实时建议**: 根据市场行情提供动态定价建议
- **对话历史**: 完整的对话记录和分析结果导出
- **快捷问题**: 预设常用问题，快速获取专业建议

##### AI功能特点
- **多模型支持**: GLM-4-Plus、GLM-4-Air、GLM-4-AirX、GLM-4-FlashX
- **专业领域**: 专注零售价格管理、市场分析、库存优化
- **安全存储**: API密钥本地加密存储
- **智能分析**: 自动提取对话关键点和标签分类
- **导出功能**: 支持对话历史JSON格式导出

##### 使用步骤
1. **配置API**: 输入GLM-4 API密钥并选择模型
2. **测试连接**: 验证API连接状态
3. **开始对话**: 询问价格策略、市场分析等问题
4. **查看分析**: 右侧面板显示AI分析摘要和关键标签

## 🚀 快速开始

### 环境要求

- **Node.js**: 14.0.0 或更高版本
- **npm**: 6.0.0 或更高版本
- **浏览器**: 支持WebSocket的现代浏览器

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd SmartTip
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **安装开发依赖**（可选，用于同时启动前后端）
   ```bash
   npm install --save-dev concurrently
   ```

4. **启动服务器**
   ```bash
   # 方式1: 使用启动脚本（推荐）
   node start.js

   # 方式2: 分别启动
   # 启动WebSocket服务器
   npm run dev
   # 启动前端HTTP服务器（新终端）
   npm run frontend

   # 方式3: 同时启动（需要concurrently）
   npm run dev:all
   ```

5. **访问前端界面**
   - 打开浏览器访问: `http://localhost:3000`
   - WebSocket服务器地址: `ws://localhost:8080`

### 🎯 使用方法

1. **连接服务器**
   - 在前端界面输入服务器地址（默认：localhost）
   - 输入端口号（默认：8080）
   - 点击"连接服务器"按钮

2. **发送消息**
   - 连接成功后，在消息输入框中输入内容
   - 点击"发送消息"或按Enter键发送
   - 消息会自动转发给所有其他连接的客户端

3. **查看状态**
   - 连接状态：实时显示连接状态
   - 系统信息：显示发送/接收消息数、连接时长等
   - 消息历史：显示所有收发的消息记录

## ⚙️ 配置说明

### 后端配置

服务器配置文件位于 `backend/config.js`，支持以下配置项：

#### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `WS_PORT` | 8080 | WebSocket服务器端口 |
| `WS_HOST` | localhost | WebSocket服务器地址 |
| `HTTP_PORT` | 3000 | 前端HTTP服务器端口 |
| `HTTP_HOST` | localhost | 前端HTTP服务器地址 |
| `MAX_CONNECTIONS` | 100 | 最大连接数 |
| `HEARTBEAT_INTERVAL` | 30000 | 心跳检测间隔(毫秒) |
| `LOG_LEVEL` | info | 日志级别(debug/info/warn/error) |
| `ENABLE_RATE_LIMIT` | true | 是否启用速率限制 |
| `MAX_MESSAGES_PER_MINUTE` | 60 | 每分钟最大消息数 |

#### 配置示例

```bash
# 设置环境变量
export WS_PORT=9090          # WebSocket端口
export WS_HOST=0.0.0.0       # WebSocket地址
export HTTP_PORT=8080        # 前端HTTP端口
export HTTP_HOST=0.0.0.0     # 前端HTTP地址
export MAX_CONNECTIONS=200   # 最大连接数
export LOG_LEVEL=debug       # 日志级别

# 启动所有服务器
node start.js
```

### 前端配置

前端配置文件位于 `frontend/config.js`，主要配置项：

- **连接配置**: 默认服务器地址、重连设置
- **界面配置**: 消息显示限制、自动滚动等
- **存储配置**: 本地存储键名和配额限制

## 📊 监控和日志

### 性能监控

系统提供实时性能监控，包括：

- **连接统计**: 当前连接数、总连接数
- **消息统计**: 消息发送/接收数量、处理速率
- **系统资源**: 内存使用、CPU占用
- **错误统计**: 错误数量和类型

### 日志系统

支持多级别日志记录：

- **控制台输出**: 彩色日志，便于开发调试
- **文件日志**: 可选的文件日志记录（需配置）
- **结构化数据**: JSON格式，便于日志分析

日志级别：
- `debug`: 详细调试信息
- `info`: 一般信息记录
- `warn`: 警告信息
- `error`: 错误信息

## 🛡️ 安全特性

### 速率限制

- **IP级别限制**: 基于客户端IP的消息频率限制
- **自动封禁**: 超过限制自动临时封禁
- **动态解封**: 封禁期满自动解除

### 输入验证

- **消息大小限制**: 防止过大消息攻击
- **内容清理**: 移除潜在恶意字符
- **连接数限制**: 防止连接耗尽攻击

## 🔧 开发指南

### 项目结构

```
SmartTip/
├── backend/                 # 后端代码
│   ├── config.js           # 配置管理
│   └── server.js           # WebSocket服务器
├── frontend/               # 前端代码
│   ├── index.html          # 主页面
│   ├── style.css           # 样式文件
│   ├── script.js           # 客户端逻辑
│   └── config.js           # 前端配置
├── frontend-server.js      # 前端HTTP服务器
├── start.js               # 启动脚本
├── package.json           # 项目配置
└── README.md             # 项目文档
```

### 开发模式

```bash
# 启用调试模式
export NODE_ENV=development
export ENABLE_DEBUG=true
export LOG_LEVEL=debug

npm run dev
```

### 代码规范

- **注释**: 代码右侧使用 `#` 注释
- **配置**: 统一配置文件管理，避免重复定义
- **错误处理**: 完善的异常捕获和用户友好提示
- **性能**: 注重代码效率和资源使用优化

## 🐛 故障排除

### 常见问题

**Q: 服务器启动失败**
```
A: 检查端口是否被占用，确认Node.js版本是否符合要求
   lsof -i :8080  # 检查端口占用
   node --version # 检查Node.js版本
```

**Q: 前端页面无法访问**
```
A: 1. 确认HTTP服务器已启动（端口3000）
   2. 检查浏览器地址：http://localhost:3000
   3. 确认frontend目录下文件完整
```

**Q: WebSocket连接失败**
```
A: 1. 确认WebSocket服务器已启动（端口8080）
   2. 检查防火墙设置
   3. 验证服务器地址和端口配置
   4. 确认前后端服务器都在运行
```

**Q: 端口被占用**
```
A: 1. 检查端口占用：lsof -i :3000 或 lsof -i :8080
   2. 设置其他端口：HTTP_PORT=3001 WS_PORT=8081
   3. 或停止占用端口的进程
```

**Q: 消息发送失败**
```
A: 1. 检查网络连接
   2. 确认消息大小未超过限制
   3. 查看是否触发速率限制
```

**Q: 性能问题**
```
A: 1. 检查系统资源使用情况
   2. 调整最大连接数配置
   3. 启用性能监控查看详细统计
```

### 启动方式
Windows：双击start.bat
其它系统：运行node start.js
分别启动：npm run dev + npm run frontend


### 日志分析

查看详细日志信息：

```bash
# 查看实时日志
tail -f logs/server-$(date +%Y-%m-%d).log

# 搜索错误日志
grep "ERROR" logs/server-*.log

# 分析连接统计
grep "性能统计" logs/server-*.log | tail -10
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 Wiki: [项目Wiki](https://github.com/your-repo/wiki)

---

**🎉 感谢使用智能标签管理系统！**

// WebSocket服务器主文件
const { WebSocketServer } = require('ws');
const config = require('./config');

console.log('智能标签管理系统WebSocket服务器');
console.log('配置加载完成:', config);

// 创建WebSocket服务器实例
const wss = new WebSocketServer({
    port: config.websocket.port,
    host: config.websocket.host
});

// 客户端连接管理
const clients = new Set();
let connectionCount = 0;

// 日志函数
function log(level, message, data = null) {
    if (config.logging.enableConsole) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        console.log(logMessage, data ? JSON.stringify(data) : '');
    }
}

// 广播消息给所有客户端(除发送者外)
function broadcastMessage(senderWs, message, isBinary = false) {
    let broadcastCount = 0;
    clients.forEach(client => {
        if (client !== senderWs && client.readyState === client.OPEN) {
            try {
                client.send(message, { binary: isBinary });
                broadcastCount++;
            } catch (error) {
                log('error', '消息发送失败', { error: error.message });
                clients.delete(client); // 移除无效连接
            }
        }
    });
    return broadcastCount;
}

// 处理新的客户端连接
wss.on('connection', function connection(ws, req) {
    connectionCount++;
    const clientId = `client_${connectionCount}`;
    const clientIP = req.socket.remoteAddress;

    // 检查连接数限制
    if (clients.size >= config.server.maxConnections) {
        log('warn', '连接数已达上限，拒绝新连接', { clientIP, maxConnections: config.server.maxConnections });
        ws.close(1013, '服务器连接数已满');
        return;
    }

    // 添加到客户端集合
    clients.add(ws);
    ws.clientId = clientId;
    ws.isAlive = true;

    log('info', '新客户端连接', { clientId, clientIP, totalClients: clients.size });

    // 发送欢迎消息
    ws.send(JSON.stringify({
        type: 'welcome',
        message: '欢迎连接到智能标签管理系统',
        clientId: clientId,
        timestamp: new Date().toISOString()
    }));

    // 处理客户端消息
    ws.on('message', function message(data, isBinary) {
        try {
            // 检查消息大小
            if (data.length > config.server.messageMaxSize) {
                log('warn', '消息过大被拒绝', { clientId, size: data.length, maxSize: config.server.messageMaxSize });
                ws.send(JSON.stringify({
                    type: 'error',
                    message: '消息过大',
                    timestamp: new Date().toISOString()
                }));
                return;
            }

            log('info', '收到消息', { clientId, messageSize: data.length, isBinary });

            // 转发消息给其他所有客户端
            const broadcastCount = broadcastMessage(ws, data, isBinary);

            log('info', '消息转发完成', { clientId, broadcastCount, totalClients: clients.size });

        } catch (error) {
            log('error', '消息处理错误', { clientId, error: error.message });
        }
    });

    // 处理心跳检测
    ws.on('pong', function heartbeat() {
        ws.isAlive = true;
    });

    // 处理连接错误
    ws.on('error', function error(err) {
        log('error', '客户端连接错误', { clientId, error: err.message });
    });

    // 处理连接关闭
    ws.on('close', function close(code, reason) {
        clients.delete(ws);
        log('info', '客户端断开连接', {
            clientId,
            code,
            reason: reason.toString(),
            remainingClients: clients.size
        });
    });
});

// 心跳检测定时器
const heartbeatInterval = setInterval(function ping() {
    wss.clients.forEach(function each(ws) {
        if (ws.isAlive === false) {
            log('info', '移除无响应连接', { clientId: ws.clientId });
            clients.delete(ws);
            return ws.terminate();
        }

        ws.isAlive = false;
        ws.ping();
    });
}, config.server.heartbeatInterval);

// 服务器关闭处理
wss.on('close', function close() {
    clearInterval(heartbeatInterval);
    log('info', 'WebSocket服务器已关闭');
});

// 错误处理
wss.on('error', function error(err) {
    log('error', 'WebSocket服务器错误', { error: err.message });
});

// 服务器启动成功
wss.on('listening', function listening() {
    log('info', 'WebSocket服务器启动成功', {
        host: config.websocket.host,
        port: config.websocket.port,
        maxConnections: config.server.maxConnections
    });
    console.log(`\n🚀 服务器运行在: ws://${config.websocket.host}:${config.websocket.port}`);
    console.log('📊 服务器状态: 等待客户端连接...\n');
});

// 优雅关闭处理
process.on('SIGINT', function() {
    log('info', '收到关闭信号，正在关闭服务器...');
    clearInterval(heartbeatInterval);

    // 通知所有客户端服务器即将关闭
    clients.forEach(client => {
        if (client.readyState === client.OPEN) {
            client.send(JSON.stringify({
                type: 'server_shutdown',
                message: '服务器即将关闭',
                timestamp: new Date().toISOString()
            }));
        }
    });

    // 关闭服务器
    wss.close(() => {
        log('info', '服务器已安全关闭');
        process.exit(0);
    });
});

module.exports = wss;
